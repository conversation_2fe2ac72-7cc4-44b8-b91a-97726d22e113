@echo off

::<PERSON><PERSON><PERSON> for installing and setting up the environment needed to run elipy.
::Dependent on running in an fb environment
::Set up is for a local development of elipy
::It will install virtualenv in current python path, create an virtual environment, activate it,
::set the yml path if needed and install studio script packages from pypi
::Not installing elipy2 here since that get's install by running 'python setup.py develop' for local development

::Can be run by the following command
::path/to/script.bat name.yaml studio_script_pkg

::Or for installing without setting elipy_config and installing studio script package
::path/to/script.bat

set studio_script_pkg=
set yml_file_name=
set wheel_dir=

echo.
echo ####################################################################
echo [INFO] Validating input...
echo ####################################################################
if NOT "%1"=="" (
	set yml_file_name=%1
)

if NOT "%2"=="" (
	set studio_script_pkg=%2
)

if NOT "%3"=="" (
	set wheel_dir=%3
)

echo.
echo ####################################################################
echo [INFO] Setting up env vars...
echo ####################################################################
set virtual_dir=%GAME_ROOT%\Python\virtual
set python_exe=%GAME_ROOT%\TnT\Bin\Python\3\python.exe
set elipy_py_scripts=%virtual_dir%\Scripts
set path=%path%;%elipy_py_scripts%
set repository=https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple
set pip_version=22.1

echo.
echo ####################################################################
echo [INFO] Creating virtual environment...
echo ####################################################################

if NOT exist %virtual_dir%\ (
    ::ensure we create the virtual environment using the exact version of python that's checked into the game code repo
    ::while inheriting installed the global package context
    %python_exe% -m venv --system-site-packages %virtual_dir%
    echo [INFO] Virtual environment created: %virtual_dir%
) ELSE (
    echo [INFO] Virtual environment already exists: %virtual_dir%
)

echo.
echo ####################################################################
echo [INFO] Activating virtual environment...
echo ####################################################################
::need to "call" the venv activate batch file so the environment is preserved
::and execution can continue. Otherwise, execution will stop after activation
call %elipy_py_scripts%\activate %name%
echo [INFO] Virtual environment activated

echo.
echo ####################################################################
echo [INFO] Removing old version of ELIPY...
echo ####################################################################
python -m pip uninstall -y elipy2

echo.
echo ####################################################################
echo [INFO] Updating pip...
echo ####################################################################
if "%wheel_dir%" == "" (
    python -m pip --timeout 60 install -i %repository% --upgrade pip==%pip_version%
) ELSE (
    echo [INFO] Installing pip from: %wheel_dir%
    python -m pip install --find-links=%wheel_dir% pip==%pip_version%
)

echo.
echo ####################################################################
echo [INFO] Installing elipy2 requirements...
echo ####################################################################
if "%wheel_dir%" == "" (
    python -m pip install -i %repository% -r requirements.txt
) ELSE (
    echo [INFO] Installing requirements.txt from: %wheel_dir%
    python -m pip install --find-links=%wheel_dir% -r .\elipy2\requirements.txt
    python -m pip install --find-links=%wheel_dir% --upgrade elipy2
)

echo.
echo ####################################################################
echo [INFO] Setting ELIPY_CONFIG...
echo ####################################################################

if NOT "%yml_file_name%" == "" (
	if NOT "%studio_script_pkg%" == "" (
	    if "%wheel_dir%" == "" (
		    SET ELIPY_CONFIG=python\virtual\Lib\site-packages\%studio_script_pkg%\yml\%yml_file_name%
		    echo [INFO] ELIPY_CONFIG env variable set to:   %ELIPY_CONFIG%
        ) ELSE (
            SET ELIPY_CONFIG=%cd%\elipy-scripts\%studio_script_pkg%\yml\%yml_file_name%.yml
            echo [INFO] ELIPY_CONFIG:   %ELIPY_CONFIG%
        )
	)
)

if NOT "%studio_script_pkg%" == "" (
	echo.
	echo ####################################################################
	echo [INFO] Installing %studio_script_pkg%...
	echo ####################################################################
    if "%wheel_dir%" == "" (
        python -m pip install -i %repository% --upgrade %studio_script_pkg%
    ) ELSE (
        %elipy_py_scripts%\activate

        echo [INFO] Installing %studio_script_pkg% from: %wheel_dir%
        FOR /F %%p IN (.\elipy-scripts\requirements.txt) DO python -m pip install --find-links=%wheel_dir% %%p
    )
) ELSE (
	%elipy_py_scripts%\activate
)

echo.
echo ####################################################################
echo [INFO] Configure elipy2 for local development
echo ####################################################################
echo.

python setup.py develop

echo.
echo ####################################################################
echo [INFO] Developer setup complete!
echo ####################################################################
echo.

:: Removing this step to use this for development of elipy
:: && python -m pip install -i http://dice-pypi.dre.dice.se/ --trusted-host dice-pypi.dre.dice.se --upgrade elipy2

