"""
test_artifactory_client.py
"""

import pathlib
from mock import Magic<PERSON>ock, patch
from elipy2.artifactory_client import ArtifactoryClient


@patch("elipy2.artifactory_client.pathlib.Path", MagicMock(spec=pathlib.Path))
@patch(
    "elipy2.artifactory_client.os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args))
)
class TestArtifactoryClient(object):
    @patch("elipy2.core.extract_zip")
    @patch("elipy2.core.run")
    @patch("elipy2.artifactory_client.os.path.exists", MagicMock(return_value=False))
    def test_download_zip_artifact(self, mock_run, mock_extract_zip):
        artifactory_client = ArtifactoryClient("<EMAIL>", "apitoken")
        source_path = "bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2023.3.2.zip"
        target_directory = "C:\\artifact\\Coverity"
        download_path = artifactory_client.download_artifact(source_path, target_directory)
        exp_path = "C:\\artifact\\Coverity\\cov-analysis-win64-2023.3.2.zip"
        exp_directory = "C:\\artifact\\Coverity"
        assert download_path == "C:\\artifact\\Coverity\\cov-analysis-win64-2023.3.2"
        mock_run.assert_called_once_with(
            [
                "curl",
                "-f",
                "-u",
                "<EMAIL>:apitoken",
                "https://artifacts.ea.com/artifactory/{}".format(source_path),
                "-o",
                "{}\\cov-analysis-win64-2023.3.2.zip".format(target_directory),
            ]
        )
        mock_extract_zip.assert_called_once_with(exp_path, exp_directory)

    @patch("elipy2.core.extract_zip")
    @patch("elipy2.core.run")
    @patch("elipy2.artifactory_client.os.path.exists", MagicMock(return_value=True))
    def test_does_not_download_when_exists(self, mock_run, mock_extract_zip):
        artifactory_client = ArtifactoryClient("<EMAIL>", "apitoken")
        source_path = "bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2023.3.2.zip"
        target_directory = "C:\\artifact\\Coverity"
        download_path = artifactory_client.download_artifact(source_path, target_directory)
        assert download_path == "C:\\artifact\\Coverity\\cov-analysis-win64-2023.3.2"
        mock_run.assert_not_called()
        mock_extract_zip.assert_not_called()

    @patch("elipy2.core.extract_zip")
    @patch("elipy2.core.run")
    @patch("elipy2.artifactory_client.os.path.exists", MagicMock(return_value=True))
    def test_downloads_when_force_download_is_true(self, mock_run, mock_extract_zip):
        artifactory_client = ArtifactoryClient("<EMAIL>", "apitoken")
        source_path = "bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2023.3.2.zip"
        target_directory = "C:\\artifact\\Coverity"
        download_path = artifactory_client.download_artifact(
            source_path, target_directory, False, True
        )
        assert download_path == "C:\\artifact\\Coverity\\cov-analysis-win64-2023.3.2.zip"
        mock_run.assert_called_once()
        # Also check that it didn't unzip the file because unzip_if_archive was set to False.
        mock_extract_zip.assert_not_called()

    @patch("elipy2.core.extract_zip", MagicMock())
    @patch("elipy2.core.run", MagicMock())
    @patch("elipy2.artifactory_client.os.path.exists", MagicMock(return_value=True))
    def test_returns_correct_path_when_downloading_file(self):
        artifactory_client = ArtifactoryClient("<EMAIL>", "apitoken")
        source_path = "dreeu-generic-local/coverity/file.txt"
        target_directory = "C:\\artifact\\Coverity"
        download_path = artifactory_client.download_artifact(
            source_path, target_directory, False, True
        )
        assert download_path == "C:\\artifact\\Coverity\\file.txt"
