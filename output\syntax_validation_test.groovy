// Simple syntax validation test for CH1_content_dev.groovy changes
// This checks if our list modifications have correct syntax

// Test frosty_downstream_matrix structure
def testFrostyMatrix() {
    def frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],  // NEW ENTRY
        [name: '.win64.upload_to_steam.combine.ww.final', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
        [name: '.win64.upload_to_steam.combine.ww.retail', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
    ]
    return frosty_downstream_matrix.size() == 5 && 
           frosty_downstream_matrix[2].name == '.spin.linux64.files.final.ww'
}

// Test shift_downstream_matrix structure
def testShiftMatrix() {
    def shift_downstream_matrix = [
        [name: 'CH1-playtest-sp.frosty.start', args: ['code_changelist', 'data_changelist']],
    ]
    return shift_downstream_matrix.size() == 1 && 
           shift_downstream_matrix[0].name == 'CH1-playtest-sp.frosty.start'
}

// Run validation tests
println "Syntax Validation Results:"
println "=========================="
println "Frosty Matrix: " + (testFrostyMatrix() ? "✅ PASS" : "❌ FAIL")
println "Shift Matrix:  " + (testShiftMatrix() ? "✅ PASS" : "❌ FAIL")

if (testFrostyMatrix() && testShiftMatrix()) {
    println "\n🎉 SUCCESS: All syntax validations passed!"
    println "The CH1_content_dev.groovy changes are syntactically correct."
} else {
    println "\n❌ FAILURE: Syntax validation failed!"
}
