"""
test_shift_utils.py
"""
import collections
import datetime
import os
import pytest
import sys
import tempfile
import time
from mock import <PERSON>Mock, mock_open, patch
from mock.mock import ANY
from requests.exceptions import HTTPError
import elipy2
from elipy2 import shifters
from elipy2.config import ConfigManager
from elipy2.exceptions import ELIPYException

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path, default_location="shift_tests")


class TestShift(object):
    def setup(self):
        self.patcher_core__run = patch("elipy2.core._run")
        self.mock_core__run = self.patcher_core__run.start()

        self.patcher_robocopy = patch("elipy2.filer.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        # mock bilbo
        self.patcher_get_builds = patch(
            "elipy2.build_metadata.BuildMetadataManager.get_builds_matching"
        )
        self.mock_get_builds = self.patcher_get_builds.start()

        self.patcher_register_shift = patch(
            "elipy2.build_metadata.BuildMetadataManager.register_shifted_build"
        )
        self.mock_register_shift = self.patcher_register_shift.start()

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        self.shift = shifters.FrostyShifter(
            "some.user",
            "some-password",
            submission_path="some-submission-path",
            use_bilbo=True,
            shift_url="https://shift.ea.com",
            submission_tool="submission-too-path",
            code_branch="code_branch",
            code_changelist="code_changelist",
            data_branch="data_branch",
            data_changelist="data_changelist",
        )

        self.shift_data = {
            "directory": ["dir1"],
            "filename": ["*-V????.pkg"],
            "upload_loop_filenames": [
                "*SUMX*V????.pkg",
                "*F2PF2P*V????.pkg",
                "*SPSP*V????.pkg",
            ],
            "supplemental_files": ["chunks*.gp5", "build.json"],
            "upload_loop": "true",
            "supplementalfilepath": ["sup1"],
            "buildname": "buildname",
            "changelist": "1234",
            "buildtype": "qe",
            "version": "1.0",
            "platform": "ps4",
            "skuid": "7f92cab3-1855-4905-ac27-1c2383034e6b",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }

    def teardown(self):
        self.patcher_core__run.stop()
        self.patcher_robocopy.stop()
        self.patcher_register_shift.stop()
        self.patcher_get_builds.stop()
        self.patcher_upload_metrics.stop()

    @pytest.fixture
    def build_details_api_response(self):
        with open(
            os.path.join(os.path.dirname(__file__), "shift_web_api", "build_details_response.json")
        ) as f:
            return f.read()

    @patch("elipy2.shift_utils.uuid.uuid4")
    def test_run_upload(self, mock_uuid):
        mock_uuid.return_value = 111
        with tempfile.NamedTemporaryFile() as submission_tool:
            for path, success in [(submission_tool.name, True), ("submission-too-path", False)]:
                shift_local = shifters.FrostyShifter(
                    "some.user",
                    "some-password",
                    submission_path="some-submission-path",
                    use_bilbo=True,
                    shift_url="https://shift.ea.com",
                    submission_tool=path,
                )

                self.mock_core__run.reset_mock()
                self.mock_core__run.return_value = 0, [], []
                if success:
                    shift_local.run_upload("shift.remplate", "sub\\path")
                else:
                    with pytest.raises(ELIPYException):
                        shift_local.run_upload("shift.remplate", "sub\\path")
                    continue

                self.mock_core__run.assert_called_once_with(
                    [
                        path,
                        "/template:shift.remplate",
                        "/user:some.user",
                        "/password:%SHIFT_PASSWORD%",
                        "/source:sub\\path",
                        "/webservice:https://shift.ea.com",
                    ],
                    working_dir=None,
                    print_std_out=True,
                    capture_std_out=True,
                    write_std_out=False,
                )

    @patch("elipy2.core.run", MagicMock(return_value=(1, "STDOUT...", "STDERR...")))
    def test_run_upload_raises_elipyexception_on_non_zero_shift_executable_exit_code(self):
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
            )

            with pytest.raises(ELIPYException):
                shift_local.run_upload("shift.template", "sub\\path")

    @patch("elipy2.LOGGER.error", MagicMock())
    @patch("elipy2.core.run", MagicMock(return_value=(1, "STDOUT...", "STDERR...")))
    def test_run_upload_logs_error_on_on_failure(self):
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
            )

            with pytest.raises(ELIPYException) as exc:
                shift_local.run_upload("shift.template", "sub\\path")

            assert elipy2.LOGGER.error.call_count == 5

    @patch("elipy2.shift_utils.os.listdir")
    @patch("elipy2.shift_utils.os.path.isdir")
    @patch("elipy2.shift_utils.os.path.isfile")
    def test_find_shift_builds(self, mock_isfile, mock_isdir, mock_listdir):
        mock_isfile.return_value = True
        mock_listdir.return_value = ["vff.exe", "submit.shift"]
        assert self.shift.find_builds(path="filer\\frosty") == ["filer\\frosty"]

    @patch("elipy2.shift_utils.os.listdir")
    @patch("elipy2.shift_utils.os.path.isdir")
    @patch("elipy2.shift_utils.os.path.isfile")
    def test_find_shift_builds_no_files(self, mock_isfile, mock_isdir, mock_listdir):
        mock_isfile.return_value = True
        mock_listdir.return_value = []
        assert self.shift.find_builds(path="filer\\frosty") == []

    @patch("elipy2.shift_utils.os.listdir")
    @patch("elipy2.shift_utils.os.path.isdir")
    @patch("elipy2.shift_utils.os.path.isfile")
    def test_find_shift_builds_no_path(self, mock_isfile, mock_isdir, mock_listdir):
        mock_isfile.return_value = True
        mock_isdir.return_value = False
        mock_listdir.return_value = ["vff.exe", "submit.shift"]
        with pytest.raises(ELIPYException):
            self.shift.find_builds(path="filer\\frosty")

    @patch("elipy2.shift_utils.uuid.uuid4")
    @patch("elipy2.core._run")
    @patch("os.path.exists")
    @patch("os.path.getsize", return_value=2 * 1024 * 1024)
    def test_shift_copy(self, mock_uuid, mock_run, mock_exsists, mock_path_getsize):
        mock_uuid.return_value = 111
        mock_run.return_value = 0, [], []
        mock_exsists.return_value = True
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            submission_path="some-submission-path",
            use_bilbo=True,
            compression=True,
            shift_url="https://shift.ea.com",
        )

        shift_local.shift_copy("some\\path", self.shift_data, "sub\\path", "sup\\path")
        assert self.mock_robocopy.call_count == 3

    @patch("elipy2.shift_utils.uuid.uuid4")
    def test_shift_copy_no_files(self, mock_uuid):
        shift_data = {
            "directory": ["dir1"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "buildname",
            "changelist": "1234",
            "buildtype": "qe",
            "version": "1.0",
            "platform": "ps4",
            "skuid": "123",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }

        mock_uuid.return_value = 111
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            submission_path="some-submission-path",
            use_bilbo=True,
            compression=True,
            shift_url="https://shift.ea.com",
        )

        shift_local.shift_copy("some\\path", shift_data, "sub\\path", "sup\\path")
        assert self.mock_robocopy.call_count == 1

    def test_shift_register_shift_in_bilbo(self):
        self.shift.register_shift_in_bilbo("some/path")
        now = time.time()
        timestamp = datetime.datetime.fromtimestamp(now).strftime("%Y-%m-%d %H:%M")
        self.mock_register_shift.assert_called_once_with("some/path", timestamp)

    @patch("elipy2.shift_utils.glob.glob")
    def test_validate_shiftdata_sup_file(self, mock_isfile):
        mock_isfile.side_effect = [[os.path.join("path", "final", "fil")], [], [], [], []]
        data, dir = self.shift.validate_shiftdata(self.shift_data, "path\\final")
        assert data["supplementalfilepath"] == []
        assert os.path.normpath(dir) == os.path.normpath("path/final")

    @patch("elipy2.shift_utils.glob.glob")
    def test_validate_shiftdata_sup_file_retail(self, mock_isfile):
        mock_isfile.return_value = []
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(self.shift_data, "path\\retail")

    @patch("elipy2.shift_utils.glob.glob")
    def test_validate_shiftdata(self, mock_isfile):
        mock_isfile.return_value = [os.path.join("path", "final", "fikle")]
        data, dir = self.shift.validate_shiftdata(self.shift_data, "path\\final")
        assert data["supplementalfilepath"] == ["fikle"]
        assert os.path.normpath(dir) == os.path.normpath("path/final")

    def test_validate_shiftdata_fail(self):
        data = {
            "directory": ["dir1"],
            "filename": ["fil1", "file2"],
            "supplementalfilepath": [],
            "buildname": "buildname",
            "changelist": "",
            "buildtype": "qe",
            "version": "1.0",
            "platform": "ps4",
            "skuid": "7f92cab3-1855-4905-ac27-1c2383034e6b",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(data)
        data["buildname"] = "-".join([str(x) for x in range(70)])[:50]
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(data)

    def test_validate_shiftdata_fail_sku(self):
        data = {
            "directory": ["dir1"],
            "filename": ["fil1", "file2"],
            "supplementalfilepath": ["sup1"],
            "buildname": "buildname",
            "changelist": "1234",
            "buildtype": "qe",
            "version": "1.0",
            "platform": "ps4",
            "skuid": "7f92cab3-1e6b",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(data)

    def test_validate_shiftdata_fail2(self):
        data = {
            "directory": ["dir1"],
            "filename": ["fil1", "file2"],
            "supplementalfilepath": [],
            "buildname": "buildname",
            "changelist": "123",
            "buildtype": "qe",
            "version": "1.0",
            "skuid": "7f92cab3-1855-4905-ac27-1c2383034e6b",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(data)

    def test_validate_shiftdata_name_too_long(self):
        shift_data = self.shift_data
        shift_data["buildname"] = "This is a very long buildname, which should fail validation."
        with pytest.raises(ELIPYException):
            self.shift.validate_shiftdata(shift_data)

    @patch("elipy2.shift_utils.glob.glob")
    def test_validate_shiftdata_remastered(self, mock_isfile):
        mock_isfile.return_value = [os.path.join("path", "final", "fikle")]
        shift_data = self.shift_data
        shift_data["filename"] += ["remastered"]
        data, dir = self.shift.validate_shiftdata(self.shift_data, "path\\final")
        assert data["buildname"] == "buildname RM"
        assert os.path.normpath(dir) == os.path.normpath("path/final")

    def test_reshift_check(self):
        self.mock_get_builds.return_value = [
            {"_id": "\\some\\where", "source": {"created": "1", "shift": "123"}}
        ]
        self.shift._reshift_check(["some/path"])
        self.mock_get_builds.assert_called_once_with("some/path")

    def test_reshift_check_false(self):
        self.mock_get_builds.return_value = {}
        assert self.shift._reshift_check(["some/path"]) == ["some/path"]
        self.mock_get_builds.assert_called_once_with("some/path")

    def test_reshift_check_no_bilbo(self):
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            submission_path="some-submission-path",
            use_bilbo=False,
            shift_url="https://shift.ea.com",
            submission_tool="submission-too-path",
        )
        self.mock_get_builds.return_value = {}
        assert shift_local._reshift_check(["some/path"]) == ["some/path"]

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.get_shift_data", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.shift_copy")
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.register_shift_in_bilbo")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available")
    def test_upload(
        self,
        mock_wait,
        mock_register,
        mock_upload,
        mock_template,
        mock_copy,
        mock_validate,
        mock_read,
        mock_reshift,
        mock_find,
    ):
        mock_find.return_value = ["some/path"]
        mock_reshift.return_value = ["some/path"]
        mock_validate.return_value = self.shift_data, "some/path"
        mock_read.return_value = self.shift_data
        mock_template.return_value = "temp"
        self.shift.process_shift_upload()
        assert (
            mock_register.call_count == 1
            and mock_upload.call_count == 1
            and mock_template.call_count == 1
            and mock_wait.call_count == 1
        )
        assert (
            mock_copy.call_count == 1
            and mock_validate.call_count == 1
            and mock_read.call_count == 1
        )
        assert mock_reshift.call_count == 1 and mock_find.call_count == 1

    @patch("elipy2.shift_utils.os.path.join", MagicMock(side_effect=lambda *args: "/".join(args)))
    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.get_shift_data", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.parse_frosty_filer_path")
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.shift_copy")
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.register_shift_in_bilbo")
    @patch("elipy2.shift_utils.ShiftUtils.determine_build_location")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available")
    def test_upload_patch_remaster(
        self,
        mock_wait,
        mock_determine_build_location,
        mock_register,
        mock_upload,
        mock_template,
        mock_copy,
        mock_validate,
        mock_reshift,
        mock_read_shift_file,
        mock_find,
        mock_parse,
        mock_get_config_data,
    ):
        mock_determine_build_location.return_value = "some/path"
        mock_find.return_value = ["some/path"]
        mock_reshift.return_value = ["some/path"]
        mock_validate.return_value = self.shift_data, "path\\final"
        mock_template.return_value = "temp"
        mock_read_shift_file.return_value = self.shift_data
        mock_parse.return_value = ["ps5", "digital", "patch", "ww", None]
        self.shift.use_elipy_config = True
        self.shift.process_shift_upload()
        assert (
            mock_register.call_count == 1
            and mock_upload.call_count == 1
            and mock_template.call_count == 1
            and mock_wait.call_count == 1
        )
        assert (
            mock_copy.call_count == 1
            and mock_validate.call_count == 1
            and mock_get_config_data.call_count == 1
        )

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo")
    @patch("elipy2.shift_utils.ShiftUtils.get_shift_data")
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.shift_copy")
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.register_shift_in_bilbo")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available")
    def test_upload_reshift(
        self,
        mock_wait,
        mock_register,
        mock_upload,
        mock_template,
        mock_copy,
        mock_validate,
        mock_read,
        mock_reshift,
        mock_find,
        mock_get_shift_data,
        mock_bilbo_builds,
    ):
        mock_bilbo_builds.return_value = [
            os.path.join("some", "path", "plt", "region", "format", "config")
        ]
        mock_reshift.return_value = ["some/path"]
        mock_validate.return_value = self.shift_data, "path\\final"
        mock_read.return_value = self.shift_data
        mock_template.return_value = "temp"
        self.shift.re_shift = True
        self.shift.use_elipy_config = True
        self.shift.process_shift_upload()
        assert (
            mock_upload.call_count == 1
            and mock_template.call_count == 1
            and mock_register.call_count == 1
            and mock_wait.call_count == 1
        )
        assert (
            mock_copy.call_count == 1
            and mock_validate.call_count == 1
            and mock_read.call_count == 0
        )
        assert (
            mock_reshift.call_count == 0
            and mock_find.call_count == 0
            and mock_get_shift_data.call_count == 1
            and mock_bilbo_builds.call_count == 1
        )

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.get_shift_data", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.shift_copy")
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.register_shift_in_bilbo")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available")
    def test_upload_no_bilbo(
        self,
        mock_wait,
        mock_register,
        mock_upload,
        mock_template,
        mock_copy,
        mock_validate,
        mock_read,
        mock_reshift,
        mock_find,
    ):
        mock_find.return_value = ["some/path"]
        mock_reshift.return_value = ["some/path"]
        mock_validate.return_value = self.shift_data, "path\\final"
        mock_read.return_value = self.shift_data
        mock_template.return_value = "temp"

        self.shift.use_bilbo = False

        self.shift.process_shift_upload()
        assert (
            mock_register.call_count == 0
            and mock_upload.call_count == 1
            and mock_template.call_count == 1
            and mock_wait.call_count == 1
        )
        assert (
            mock_copy.call_count == 1
            and mock_validate.call_count == 1
            and mock_read.call_count == 1
        )
        assert mock_reshift.call_count == 1 and mock_find.call_count == 1

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.get_shift_data", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.shift_copy")
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.register_shift_in_bilbo")
    def test_upload_fake_sku(
        self,
        mock_register,
        mock_upload,
        mock_template,
        mock_copy,
        mock_validate,
        mock_read,
        mock_reshift,
        mock_find,
    ):
        data = {
            "directory": ["dir1"],
            "filename": ["fil1", "file2"],
            "upload_loop_filenames": ["fil4", "file3"],
            "upload_loop": "true",
            "supplementalfilepath": ["sup1"],
            "buildname": "buildname",
            "changelist": "",
            "buildtype": "qe",
            "version": "1.0",
            "platform": "ps4",
            "skuid": "Fake_sku_id",
            "skuname": "skuname",
            "distributiontype": "distributiontype",
            "retentionpolicy": "retentionpolicy",
            "milestone": "milestone",
            "priority": "priority",
        }

        mock_find.return_value = ["some/path"]
        mock_reshift.return_value = ["some/path"]
        mock_validate.return_value = data, "path\\final"
        mock_read.return_value = data
        mock_template.return_value = "temp"
        self.shift.process_shift_upload()
        assert mock_register.call_count == 1
        assert mock_upload.call_count == 0
        assert mock_template.call_count == 0
        assert (
            mock_copy.call_count == 0
            and mock_validate.call_count == 0
            and mock_read.call_count == 1
        )
        assert mock_reshift.call_count == 1 and mock_find.call_count == 1

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils._upload_single_build")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available", MagicMock())
    def test_upload_with_exception(self, mock_upload_single_build, mock_reshift, mock_find):
        mock_find.return_value = ["some/path", "another/path"]
        mock_reshift.return_value = ["some/path", "another/path"]
        mock_upload_single_build.side_effect = Exception("Test Exception")
        with pytest.raises(ELIPYException):
            self.shift.path = ["some/path", "another/path"]
            self.shift.process_shift_upload()
        assert mock_upload_single_build.call_count == 2

    @patch("elipy2.shift_utils.ShiftUtils.find_builds_bilbo", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.find_builds")
    @patch("elipy2.shift_utils.ShiftUtils._reshift_check")
    @patch("elipy2.shift_utils.ShiftUtils._upload_single_build")
    def test_upload_with_elipyexception(self, mock_upload_single_build, mock_reshift, mock_find):
        mock_find.return_value = ["some/path", "another/path"]
        mock_reshift.return_value = ["some/path", "another/path"]
        mock_upload_single_build.side_effect = ELIPYException("Test Exception")
        with pytest.raises(ELIPYException):
            self.shift.path = ["some/path", "another/path"]
            self.shift.process_shift_upload()
        assert mock_upload_single_build.call_count == 2

    @patch("elipy2.shift_utils.uuid.uuid4")
    @patch("os.path.getsize", MagicMock(return_value=2 * 1024 * 1024))
    def test_create_template_file(self, mock_uuid):
        mock_uuid.return_value = 111
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            use_bilbo=True,
            shift_url="https://shift.ea.com",
            submission_tool="submission-too-path",
        )
        m = mock_open()
        with patch("elipy2.shift_utils.open", m):
            assert shift_local.create_template_file(
                "some/path", self.shift_data, "sup\\file"
            ) == os.path.join("some/path", "shift.template")
            m.assert_called_once_with(os.path.join("some/path", "shift.template"), "w+")

    @patch("elipy2.shift_utils.os.remove")
    @patch("elipy2.shift_utils.os.path.isfile", MagicMock())
    @patch("elipy2.shift_utils.uuid.uuid4")
    @patch("os.path.getsize", MagicMock(return_value=2 * 1024 * 1024))
    def test_create_template_file_file_exist(self, mock_uuid, mock_remove):
        mock_uuid.return_value = 111
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            use_bilbo=True,
            shift_url="https://shift.ea.com",
            submission_tool="submission-too-path",
        )
        m = mock_open()
        with patch("elipy2.shift_utils.open", m):
            assert shift_local.create_template_file(
                "some/path", self.shift_data, "sup\\file"
            ) == os.path.join("some/path", "shift.template")
            m.assert_called_once_with(os.path.join("some/path", "shift.template"), "w+")
            assert mock_remove.call_count == 1

    @patch("elipy2.shift_utils.os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.shift_utils.uuid.uuid4")
    @patch("os.path.getsize", MagicMock(return_value=2 * 1024 * 1024))
    def test_create_template_file_without_supplemental(self, mock_uuid):
        mock_uuid.return_value = 111
        shift_local = shifters.FrostyShifter(
            "some.user",
            "some-password",
            use_bilbo=True,
            shift_url="https://shift.ea.com",
            submission_tool="submission-too-path",
        )
        m = mock_open()
        with patch("elipy2.shift_utils.open", m):
            assert shift_local.create_template_file(
                "some/path", self.shift_data, "sup\\file"
            ) == os.path.join("some/path", "shift.template")
            m.assert_called_once_with(os.path.join("some/path", "shift.template"), "w+")
            handle = m()

            for call in handle.write.call_args_list:
                args, _ = call
                assert args[0] != "shift.export.version.suplemental.folderPath = sup\\file\n"

    @patch("elipy2.shift_utils.open", new_callable=mock_open, read_data="content file")
    def test_read_shift_file(self, m):
        assert self.shift.read_shift_file("some/path", "shift-file")["content"] == "file"
        m.assert_called_with(os.path.join("some/path", "shift-file"), "r")

    @patch("elipy2.shift_utils.open", new_callable=mock_open, read_data="filename file")
    def test_read_shift_file2(self, m):
        assert self.shift.read_shift_file("some/path", "shift-file")["filename"] == ["file"]

    def test_parse_config_data(self):
        data = {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }

        config = collections.OrderedDict()
        config["content"] = {"files": {"file_names": ["fil2", "fil3"]}}
        config["supplemental_files"] = ["fil2", "fil3"]
        config["directory"] = ["fil2", "fil3"]
        config["milestone"] = ""
        config["sku_id"] = ""
        config["sku_name"] = ""
        config["buildtype"] = "QA"
        config["retention_policy"] = ""
        config["distribution_type"] = "dist"
        config["priority"] = ""
        config["settings"] = {
            "files": {"final": {"distribution_type": "type", "incremental_delivery": True}}
        }
        assert self.shift.parse_config_data(config, data, ["files", "final"]) == {
            "directory": ["fil2", "fil3"],
            "filename": ["fil2", "fil3"],
            "supplementalfilepath": ["fil2", "fil3"],
            "buildname": "",
            "changelist": "",
            "buildtype": "QA",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "type",
            "incrementaldelivery": True,
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }

    def test_parse_config_data_empty(self):
        data = {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }

        config = collections.OrderedDict()
        config["content"] = {"files": {"file_names": ["fil2", "fil3"]}}
        config["supplemental_files"] = [""]
        config["directory"] = ["fil2", "fil3"]
        config["milestone"] = ""
        config["sku_id"] = ""
        config["sku_name"] = ""
        config["buildtype"] = "QA"
        config["retention_policy"] = ""
        config["distribution_type"] = "dist"
        config["priority"] = ""
        config["settings"] = {"files": {"final": {"distribution_type": "type"}}}
        assert self.shift.parse_config_data(config, data, ["files", "final"]) == {
            "directory": ["fil2", "fil3"],
            "filename": ["fil2", "fil3"],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "QA",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "type",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }

    def test_parse_config_data_version_and_upload_loop(self):
        data = {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "upload_loop_filenames": [],
        }

        config = collections.OrderedDict()
        config["content"] = {"files": {"upload_loop_filenames": ["upload1", "upload2"]}}
        config["milestone"] = ""
        config["sku_id"] = ""
        config["sku_name"] = ""
        config["buildtype"] = "QA"
        config["retention_policy"] = ""
        config["distribution_type"] = "dist"
        config["priority"] = ""
        config["settings"] = {"files": {"final": {"distribution_type": "type"}}}
        config["upload_loop"] = "true"
        config["version"] = "2.0"
        assert self.shift.parse_config_data(config, data, ["files", "final"]) == {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "QA",
            "version": "2.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "type",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "upload_loop": "true",
            "upload_loop_filenames": ["upload1", "upload2"],
        }

    def test_parse_config_data_content_layer(self):
        data = {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "content_layer": "content_layer",
        }

        config = collections.OrderedDict()
        config["content"] = {"files": {"file_names": ["fil2", "fil3"]}}
        config["supplemental_files"] = ["fil2", "fil3"]
        config["directory"] = ["fil2", "fil3"]
        config["milestone"] = ""
        config["sku_id"] = ""
        config["sku_name"] = ""
        config["buildtype"] = "QA"
        config["retention_policy"] = ""
        config["distribution_type"] = "dist"
        config["priority"] = ""
        config["settings"] = {
            "files": {
                "final": {
                    "source": {"distribution_type": "typeA", "incremental_delivery": False},
                    "CONTENT_LAYER": {"distribution_type": "typeB", "incremental_delivery": True},
                }
            }
        }
        assert self.shift.parse_config_data(config, data, ["files", "final", "content_layer"]) == {
            "directory": ["fil2", "fil3"],
            "filename": ["fil2", "fil3"],
            "supplementalfilepath": ["fil2", "fil3"],
            "buildname": "",
            "changelist": "",
            "content_layer": "content_layer",
            "buildtype": "QA",
            "version": "1.0",
            "skuid": "",
            "skuname": "",
            "distributiontype": "typeB",
            "incrementaldelivery": True,
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }

    def test_get_config_path(self):
        assert all(
            folder in self.shift.get_config_path()
            for folder in ["elipy2", "elipy_example_shift.yml"]
        )

    @patch(
        "elipy2.shift_utils.ShiftUtils.get_config_path",
        MagicMock(
            return_value=os.path.join(os.path.dirname(__file__), "..", "elipy_example_shift.yml")
        ),
    )
    def test_get_config_data(self):
        assert self.shift.get_config_data() == collections.OrderedDict(
            [
                ("buildtype", "QA"),
                ("milestone", "Production"),
                ("distribution_type", "InternalOnly"),
                ("retention_policy", "SpaceAvailable"),
                ("priority", ""),
                (
                    "xb1",
                    collections.OrderedDict(
                        [
                            (
                                "content",
                                collections.OrderedDict(
                                    [
                                        ("file_names", ["appdata.bin"]),
                                        ("supplemental_files", [""]),
                                        ("directory", ["Config", "Data", "Scripts"]),
                                        (
                                            "digital",
                                            collections.OrderedDict(
                                                [
                                                    ("file_names", ["appxmanifest.xml", "*.ekb"]),
                                                    ("supplemental_files", ["build.json"]),
                                                    ("directory", [""]),
                                                ]
                                            ),
                                        ),
                                    ]
                                ),
                            ),
                            (
                                "settings",
                                collections.OrderedDict(
                                    [
                                        (
                                            "dice-dev",
                                            collections.OrderedDict(
                                                [
                                                    (
                                                        "final",
                                                        collections.OrderedDict(
                                                            [
                                                                (
                                                                    "patch",
                                                                    collections.OrderedDict(
                                                                        [
                                                                            (
                                                                                "sku_id",
                                                                                "7f92cab3-1855-4905-ac27-1c2383034e6b",
                                                                            ),
                                                                            (
                                                                                "sku_name",
                                                                                "Patch - WW (Digital Final)",
                                                                            ),
                                                                        ]
                                                                    ),
                                                                )
                                                            ]
                                                        ),
                                                    )
                                                ]
                                            ),
                                        )
                                    ]
                                ),
                            ),
                        ]
                    ),
                ),
            ]
        )

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_without_build_and_sku_name_different_code_data_changelists(
        self, mock_get_data, mock_parse_data
    ):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

        changelist_label = "1234_5678"
        name = "{} {} {}".format("f", "c", changelist_label)

        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="q",
            data_changelist="5678",
            data_branch="q",
            platform="a",
            config="c",
            format="f",
            region="na",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": name,
            "changelist": changelist_label,
            "buildtype": "",
            "version": "1.0",
            "platform": "a",
            "skuid": "",
            "skuname": name,
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_without_build_and_sku_name_same_code_data_changelists(
        self, mock_get_data, mock_parse_data
    ):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

        changelist_label = "1234"
        name = "{} {} {}".format("f", "c", changelist_label)

        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="q",
            data_changelist="1234",
            data_branch="q",
            platform="a",
            config="c",
            format="f",
            region="na",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": name,
            "changelist": changelist_label,
            "buildtype": "",
            "version": "1.0",
            "platform": "a",
            "skuid": "",
            "skuname": name,
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_with_build_and_sku_name_different_code_data_changelists(
        self, mock_get_data, mock_parse_data
    ):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "few",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "few",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

        changelist_label = "1234_5678"
        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="q",
            data_changelist="5678",
            data_branch="q",
            platform="a",
            config="c",
            format="f",
            region="na",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "few",
            "changelist": changelist_label,
            "buildtype": "",
            "version": "1.0",
            "platform": "a",
            "skuid": "",
            "skuname": "few",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_with_build_and_sku_name_same_code_data_changelists(
        self, mock_get_data, mock_parse_data
    ):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "few",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "few",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

        changelist_label = "1234"
        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="q",
            data_changelist="1234",
            data_branch="q",
            platform="a",
            config="c",
            format="f",
            region="na",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "few",
            "changelist": changelist_label,
            "buildtype": "",
            "version": "1.0",
            "platform": "a",
            "skuid": "",
            "skuname": "few",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_parse_branch_data(self, mock_get_config_data, mock_parse_config_data):
        mock_get_config_data.return_value = {"config": "data"}
        shift_data = {
            "directory": [],
            "filename": [],
            "upload_loop_filenames": [],
            "upload_loop": "",
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "content_layer": "source",
        }
        parse_list = [
            "data_branch",
            "platform",
            "config",
            "format",
            "region",
            "source",
        ]
        self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="code_branch",
            data_changelist="5678",
            data_branch="data_branch",
            platform="platform",
            config="config",
            format="format",
            region="region",
        )
        mock_parse_config_data.assert_called_once_with({"config": "data"}, shift_data, parse_list)

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_parse_branch_code(self, mock_get_config_data, mock_parse_config_data):
        mock_get_config_data.return_value = {"config": "data"}
        shift_data = {
            "directory": [],
            "filename": [],
            "upload_loop_filenames": [],
            "upload_loop": "",
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "content_layer": "source",
        }
        parse_list = [
            "code_branch",
            "platform",
            "config",
            "format",
            "region",
            "source",
        ]
        self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="code_branch",
            data_changelist=None,
            data_branch=None,
            platform="platform",
            config="config",
            format="format",
            region="region",
        )
        mock_parse_config_data.assert_called_once_with({"config": "data"}, shift_data, parse_list)

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_changelist_label_both(self, mock_get_data, mock_parse_data):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "build",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "sku",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }
        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="code_branch",
            data_changelist="5678",
            data_branch="data_branch",
            platform="platform",
            config="config",
            format="format",
            region="region",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "build",
            "changelist": "1234_5678",
            "buildtype": "",
            "version": "1.0",
            "platform": "platform",
            "skuid": "",
            "skuname": "sku",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.ShiftUtils.parse_config_data")
    @patch("elipy2.shift_utils.ShiftUtils.get_config_data")
    def test_get_shift_data_changelist_label_code_only(self, mock_get_data, mock_parse_data):
        mock_get_data.return_value = ""
        mock_parse_data.return_value = {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "build",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "sku",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }
        assert self.shift.get_shift_data(
            code_changelist="1234",
            code_branch="code_branch",
            data_changelist=None,
            data_branch=None,
            platform="platform",
            config="config",
            format="format",
            region="region",
        ) == {
            "directory": ["va"],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "build",
            "changelist": "1234",
            "buildtype": "",
            "version": "1.0",
            "platform": "platform",
            "skuid": "",
            "skuname": "sku",
            "distributiontype": "fe",
            "retentionpolicy": "fe",
            "milestone": "",
            "priority": "fe",
        }

    @patch("elipy2.shift_utils.os.path.isdir")
    def test_find_builds_bilbo(self, mock_is_dir):
        objects = [
            elipy2.bilbo.Build().from_dict({"_id": "\\some\\where", "_source": {"created": "1"}}),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\over\\the\\bundles",
                    "_source": {
                        "created": "2",
                        "deleted": "hello",
                    },
                }
            ),
        ]

        mock_is_dir.return_value = True
        self.mock_get_builds.return_value = objects
        assert self.shift.find_builds_bilbo(path="filer\\frosty") == ["\\some\\where"]

    @patch("elipy2.shift_utils.ShiftUtils.post_to_shift_api")
    def test_set_retention_policy(self, mock_api_call):
        self.shift.set_retention_policy("12333")
        mock_api_call.assert_called_once_with(
            function_path="/nuxeo/site/automation/UpdateVersionRetentionPolicy",
            params='{"params": {"versionId": "12333", "retentionPolicyId": "Archive"}}',
        )

    @pytest.mark.skipif(not sys.platform.startswith("win"), reason="Not running on windows")
    @patch("requests.get")
    def test_download_files(self, mock_requests):
        mock_requests.return_value.ok = True
        mock_response = MagicMock()
        mock_response.content = b"unit-test test_download_files"

        for path, success, ignore_error, mock_requests.side_effect in [
            ("downloads", True, False, [mock_response, mock_response, mock_response]),
            ("nonexistent", False, False, [HTTPError(), HTTPError(), HTTPError()]),
            ("nonexistent", False, True, [HTTPError(), HTTPError(), HTTPError()]),
            ("flaky", True, False, [HTTPError(), HTTPError(), mock_response]),
        ]:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                artifactory_user="some.user",
                artifactory_api_key="some.key",
            )
            if success:
                shift_local._download_file(
                    "https://python.org", path, ".", ignore_error=ignore_error
                )
            else:
                with pytest.raises(HTTPError):
                    shift_local._download_file("https://python.org", path, ".")
                continue

            assert os.path.exists(path) != ignore_error
            if not ignore_error:
                os.remove(path)

    @patch("elipy2.shift_utils.os.makedirs", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils._download_file")
    @patch("elipy2.shift_utils.os.path.exists")
    @patch("elipy2.shift_utils.frostbite_core.get_tnt_root")
    @patch("elipy2.shift_utils.local_paths.get_tnt_local_path")
    @patch("elipy2.shift_utils.SETTINGS.get")
    def test_get_shift_submission_tool(
        self,
        mock_settings_get,
        mock_local_path,
        mock_tnt_root,
        mock_exists,
        mock_download,
    ):
        mock_tnt_root.return_value = mock_local_path.return_value = "some\\path"
        default_path = os.path.join(
            mock_tnt_root.return_value, "build", "bin", "ShiftSubmit", "ShiftSubmit.exe"
        )
        version = "5.1.0"
        version_folder_path = os.path.join(mock_local_path.return_value, "shiftsubmit", version)
        version_exe_path = os.path.join(version_folder_path, "ShiftSubmission.exe")

        mock_download.return_value = version_exe_path

        mock_settings_get.side_effect = lambda key, default=None: (
            f"https://artifacts.ea.com/artifactory/list/dre-generic-federated/cobra/shiftsubmission/{version}/"
            if key == "shift_tool_url"
            else default
        )

        with tempfile.NamedTemporaryFile() as temp_file:
            for tool_path, exists, expected_path in [
                (temp_file.name, [True], temp_file.name),
                (None, [True], default_path),
                (None, [False, True], version_exe_path),
            ]:
                shift_local = shifters.FrostyShifter(
                    "some.user",
                    "some-password",
                    submission_tool=tool_path,
                    artifactory_user="some.user",
                    artifactory_api_key="some.key",
                )
                mock_exists.side_effect = exists

                return_path = shift_local._get_shift_submission_tool()
                assert return_path == expected_path

    def test_call_process_shift_upload_directly(self):
        with pytest.raises(NotImplementedError):
            elipy2.shift_utils.ShiftUtils.process_shift_upload(self)

    def test_call_determine_build_location_directly(self):
        with pytest.raises(NotImplementedError):
            elipy2.shift_utils.ShiftUtils.determine_build_location(self)

    def test_run_upload_extract_build_id(self):
        stdout_lines = [
            "Details:",
            " Version title:Production 626 - files final 20707243_5113197",
            " Version Id:da43ac76-214c-4625-bd11-f491e2851625",
            "Submission to shift successful and validation will begin shortly.",
        ]
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
            )

            with patch("elipy2.core.run", MagicMock(return_value=(0, stdout_lines, []))):
                expected_build_id = "da43ac76-214c-4625-bd11-f491e2851625"
                result = shift_local.run_upload("shift.template", "sub\\path")
                assert result == expected_build_id

    def test_run_upload_extract_build_id_not_found(self):
        stdout_lines = [
            "Details:",
            " Version title:Production 626 - files final 20707243_5113197",
            " No version id in shift response",
            "Submission to shift successful and validation will begin shortly.",
        ]
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
            )

            with patch("elipy2.core.run", MagicMock(return_value=(0, stdout_lines, []))):
                result = shift_local.run_upload("shift.template", "sub\\path")
                assert result == None

    @patch("elipy2.shift_utils.ShiftUtils.shift_copy", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.create_template_file", MagicMock())
    @patch("elipy2.shift_utils.ShiftUtils.set_retention_policy")
    @patch("elipy2.shift_utils.ShiftUtils.run_upload")
    @patch("elipy2.shift_utils.ShiftUtils.validate_shiftdata")
    @patch("elipy2.shift_utils.ShiftUtils.read_shift_file")
    @patch("elipy2.shift_utils.ShiftUtils._wait_until_build_available", MagicMock())
    def test_upload_build_set_retention_policy(
        self, mock_read, mock_validate, mock_upload, mock_retention
    ):
        mock_read.return_value = self.shift_data
        mock_validate.return_value = self.shift_data, "path\\final"
        mock_upload.return_value = "some-build-id"
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
                shift_retention_policy="Archive",
            )

            shift_local._upload_single_build(
                "some-shift-path",
                "some-shift-file",
                "some-code-changelist",
                "some-data-changelist",
                "some-code-branch",
                "some-data-branch",
            )
            mock_retention.assert_called_once_with("some-build-id", "Archive")

    @patch("elipy2.shift_utils.ShiftUtils.get_build_status")
    def test_wait_until_build_available(self, mock_get_status):
        mock_get_status.side_effect = ["Processing", "Available"]
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
                shift_retention_policy="Archive",
            )
        shift_local._wait_until_build_available("some-build-id", interval=2)
        assert mock_get_status.call_count == 2

    @patch("elipy2.shift_utils.ShiftUtils.get_build_status")
    def test_wait_until_build_available_no_build_id(self, mock_get_status):
        mock_get_status.side_effect = ["Processing", "Available"]
        with tempfile.NamedTemporaryFile() as submission_tool:
            shift_local = shifters.FrostyShifter(
                "some.user",
                "some-password",
                submission_path="some-submission-path",
                use_bilbo=True,
                shift_url="https://shift.ea.com",
                submission_tool=submission_tool.name,
                shift_retention_policy="Archive",
            )
        shift_local._wait_until_build_available(None)
        mock_get_status.assert_not_called()

    @patch("elipy2.shift_utils.ShiftUtils.post_to_shift_api")
    @patch("json.loads", MagicMock())
    def test_get_build_status_api_call(self, mock_api_call):
        self.shift.get_build_status("12345")
        mock_api_call.assert_called_once_with(
            function_path="/nuxeo/site/automation/GetBuildDetails",
            params='{"params": {"Build ID": "12345"}}',
        )

    @patch("elipy2.shift_utils.ShiftUtils.post_to_shift_api")
    def test_get_build_status_return(self, mock_api_call, build_details_api_response):
        mock_response = MagicMock()
        mock_response.content = build_details_api_response
        mock_api_call.return_value = mock_response
        assert self.shift.get_build_status("12345") == "Available"

    @patch("elipy2.shifters.FrostyShifter.get_builds_by_sku")
    def test_get_latest_build_id_by_sku(self, mock_get_builds_by_sku):
        mock_get_builds_by_sku.return_value = [
            {
                "Build ID": "104fbf96-f727-4dda-9a85-442b738fed0c",
                "Submission Date": "01 Jan 2024 10:00 AM (UTC)",
            },
            {
                "Build ID": "55e19d05-6e58-450e-ad6f-b9e2ad2b9d6e",
                "Submission Date": "02 Jan 2024 10:00 AM (UTC)",
            },
            {
                "Build ID": "d9fe9fca-feef-4e88-aeb8-61e01c0cc007",
                "Submission Date": "03 Jan 2024 10:00 AM (UTC)",
            },
        ]

        latest_build_id = self.shift.get_latest_build_id_by_sku("some_sku_id")
        assert latest_build_id == "d9fe9fca-feef-4e88-aeb8-61e01c0cc007"

        mock_get_builds_by_sku.return_value = []
        latest_build_id = self.shift.get_latest_build_id_by_sku("some_sku_id")
        assert latest_build_id is None

    @patch("elipy2.shifters.FrostyShifter._wait_until_build_available")
    @patch("elipy2.shifters.FrostyShifter.run_upload")
    @patch("elipy2.shifters.FrostyShifter.validate_shiftdata")
    @patch("elipy2.shifters.FrostyShifter.create_template_file", return_value="template_file")
    @patch("elipy2.shifters.FrostyShifter.shift_copy")
    @patch("elipy2.shifters.FrostyShifter.set_retention_policy")
    @patch("elipy2.shifters.FrostyShifter.get_latest_build_id_by_sku", return_value="base_build_id")
    def test_upload_single_build_with_incremental_delivery_and_build_id_set(
        self,
        mock_get_latest_build_id_by_sku,
        mock_set_retention_policy,
        mock_shift_copy,
        mock_create_template_file,
        mock_validate_shiftdata,
        mock_run_upload,
        mock_wait,
    ):
        self.shift_data["incrementaldelivery"] = True
        self.shift.build_id = "existing_build_id"

        mock_validate_shiftdata.return_value = self.shift_data, "shift\\dir"

        with patch.object(self.shift, "read_shift_file", return_value=self.shift_data):
            self.shift._upload_single_build(
                "shift_dir",
                code_changelist="code_changelist",
                data_changelist="data_changelist",
                code_branch="code_branch",
                data_branch="data_branch",
            )

        mock_run_upload.assert_called_once_with("template_file", ANY, "existing_build_id")
        mock_get_latest_build_id_by_sku.assert_not_called()

    @patch("elipy2.shifters.FrostyShifter._wait_until_build_available")
    @patch("elipy2.shifters.FrostyShifter.run_upload")
    @patch("elipy2.shifters.FrostyShifter.validate_shiftdata")
    @patch("elipy2.shifters.FrostyShifter.create_template_file", return_value="template_file")
    @patch("elipy2.shifters.FrostyShifter.shift_copy")
    @patch("elipy2.shifters.FrostyShifter.set_retention_policy")
    @patch("elipy2.shifters.FrostyShifter.get_latest_build_id_by_sku", return_value="base_build_id")
    def test_upload_single_build_with_incremental_delivery_and_no_build_id(
        self,
        mock_get_latest_build_id_by_sku,
        mock_set_retention_policy,
        mock_shift_copy,
        mock_create_template_file,
        mock_validate_shiftdata,
        mock_run_upload,
        mock_wait,
    ):
        self.shift_data["incrementaldelivery"] = True
        self.shift.build_id = None

        mock_validate_shiftdata.return_value = self.shift_data, "shift\\dir"

        with patch.object(self.shift, "read_shift_file", return_value=self.shift_data):
            self.shift._upload_single_build(
                "shift_dir",
                code_changelist="code_changelist",
                data_changelist="data_changelist",
                code_branch="code_branch",
                data_branch="data_branch",
            )

        mock_run_upload.assert_called_once_with("template_file", ANY, "base_build_id")
        mock_get_latest_build_id_by_sku.assert_called_once_with(self.shift_data["skuid"])

    @patch("elipy2.shifters.FrostyShifter._wait_until_build_available")
    @patch("elipy2.shifters.FrostyShifter.run_upload")
    @patch("elipy2.shifters.FrostyShifter.validate_shiftdata")
    @patch("elipy2.shifters.FrostyShifter.create_template_file", return_value="template_file")
    @patch("elipy2.shifters.FrostyShifter.shift_copy")
    @patch("elipy2.shifters.FrostyShifter.set_retention_policy")
    @patch("elipy2.shifters.FrostyShifter.get_latest_build_id_by_sku", return_value=None)
    def test_upload_single_build_without_incremental_delivery(
        self,
        mock_get_latest_build_id_by_sku,
        mock_set_retention_policy,
        mock_shift_copy,
        mock_create_template_file,
        mock_validate_shiftdata,
        mock_run_upload,
        mock_wait,
    ):
        self.shift_data["incrementaldelivery"] = False

        mock_validate_shiftdata.return_value = self.shift_data, "shift\\dir"

        with patch.object(self.shift, "read_shift_file", return_value=self.shift_data):
            self.shift._upload_single_build(
                "shift_dir",
                code_changelist="code_changelist",
                data_changelist="data_changelist",
                code_branch="code_branch",
                data_branch="data_branch",
            )

        mock_run_upload.assert_called_once_with("template_file", ANY, None)
        mock_get_latest_build_id_by_sku.assert_not_called()

    @patch(
        "elipy2.shifters.FrostyShifter._get_shift_submission_tool",
        return_value="shift_submit_tool_path",
    )
    @patch(
        "elipy2.core.run", return_value=(0, ["Version Id:123e4567-e89b-12d3-a456-426614174000"], [])
    )
    def test_run_upload_with_base_build_id(self, mock_run, mock_get_tool):
        template_file = "template_file"
        submission_dest = "submission_dest"
        base_build_id = "base_build_id"

        self.shift.run_upload(template_file, submission_dest, base_build_id)

        expected_cmd = [
            "shift_submit_tool_path",
            "/template:template_file",
            "/user:some.user",
            "/password:%SHIFT_PASSWORD%",
            "/source:submission_dest",
            "/webservice:https://shift.ea.com",
            f"/diff-patch-base:{base_build_id}",
        ]

        mock_run.assert_called_once_with(
            expected_cmd, print_std_out=True, env_patch={"SHIFT_PASSWORD": "some-password"}
        )

    @patch(
        "elipy2.shifters.FrostyShifter._get_shift_submission_tool",
        return_value="shift_submit_tool_path",
    )
    @patch(
        "elipy2.core.run", return_value=(0, ["Version Id:123e4567-e89b-12d3-a456-426614174000"], [])
    )
    def test_run_upload_without_base_build_id(self, mock_run, mock_get_tool):
        template_file = "template_file"
        submission_dest = "submission_dest"

        self.shift.run_upload(template_file, submission_dest)

        expected_cmd = [
            "shift_submit_tool_path",
            "/template:template_file",
            "/user:some.user",
            "/password:%SHIFT_PASSWORD%",
            "/source:submission_dest",
            "/webservice:https://shift.ea.com",
        ]

        mock_run.assert_called_once_with(
            expected_cmd, print_std_out=True, env_patch={"SHIFT_PASSWORD": "some-password"}
        )

    def test_validate_shiftdata_files_present(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            open(os.path.join(temp_dir, "file-V1234.pkg"), "a").close()

            self.shift.validate_shiftdata(self.shift_data, temp_dir)

    def test_validate_upload_loop_files_present(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            open(os.path.join(temp_dir, "SUMX-V1234.pkg"), "a").close()

            self.shift.validate_shiftdata(self.shift_data, temp_dir)

    def test_validate_supplemental_files_present(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test")
            os.mkdir(test_dir)
            open(os.path.join(test_dir, "file-V1234.pkg"), "a").close()

            open(os.path.join(test_dir, "chunks1234.gp5"), "a").close()
            open(os.path.join(temp_dir, "build.json"), "a").close()
            shift_data = {
                "directory": ["dir1"],
                "filename": ["*-V[0-9][0-9][0-9][0-9].pkg"],
                "upload_loop_filenames": [
                    "*SUMX*V[0-9][0-9][0-9][0-9].pkg",
                    "*F2PF2P*V[0-9][0-9][0-9][0-9].pkg",
                    "*SPSP*V[0-9][0-9][0-9][0-9].pkg",
                ],
                "supplemental_files": ["chunks*.gp5", "build.json"],
                "upload_loop": "true",
                "supplementalfilepath": ["chunks*.gp5", "build.json"],
                "buildname": "buildname",
                "changelist": "1234",
                "buildtype": "qe",
                "version": "1.0",
                "platform": "ps4",
                "skuid": "7f92cab3-1855-4905-ac27-1c2383034e6b",
                "skuname": "skuname",
                "distributiontype": "distributiontype",
                "retentionpolicy": "retentionpolicy",
                "milestone": "milestone",
                "priority": "priority",
            }
            data, dir = self.shift.validate_shiftdata(shift_data, temp_dir)
            assert data["supplementalfilepath"] == ["chunks1234.gp5", "build.json"]
            assert dir == test_dir

    def test_parse_filer_frosty_path(self):
        path = os.path.join(
            "filer.dice.ad.ea.com",
            "Builds",
            "Battlefield",
            "frosty",
            "BattlefieldGame",
            "CH1-content-dev",
            "22846060",
            "CH1-content-dev",
            "22846060",
            "xbsx",
            "files",
            "ww",
            "final",
        )
        assert self.shift.parse_frosty_filer_path(path) == ("xbsx", "final", "files", "ww", None)

    def test_parse_filer_frosty_path_with_content_layer(self):
        path = os.path.join(
            " ",
            " ",
            "filer.dice.ad.ea.com",
            "Builds",
            "Battlefield",
            "frosty",
            "BattlefieldGame",
            "bf-playtest-san",
            "21586518",
            "trunk-code-dev",
            "21586518",
            "ps5",
            "ContentLayer_C1S2B1",
            "files",
            "playtest",
            "performance",
        )
        assert self.shift.parse_frosty_filer_path(path) == (
            "ps5",
            "performance",
            "files",
            "playtest",
            "C1S2B1",
        )

    def test_parse_filer_frosty_path_with_content_layer_lowercase(self):
        path = os.path.join(
            "filer.dice.ad.ea.com",
            "builds",
            "battlefield",
            "frosty",
            "battlefieldgame",
            "bf-playtest-san",
            "21586518",
            "trunk-code-dev",
            "21586518",
            "ps5",
            "contentLayer_c1s2b1",
            "files",
            "playtest",
            "performance",
        )
        assert self.shift.parse_frosty_filer_path(path) == (
            "ps5",
            "performance",
            "files",
            "playtest",
            "c1s2b1",
        )

    def test_parse_filer_frosty_path_with_combine(self):
        path = os.path.join(
            "filer.dice.ad.ea.com",
            "Builds",
            "Battlefield",
            "frosty",
            "BattlefieldGame",
            "bf-playtest-san",
            "21586518",
            "trunk-code-dev",
            "21586518",
            "ps5",
            "digital_combined",
            "patch_combine",
            "final",
            "some-combine-data",
            "5678",
            "some-combine-code",
            "8765",
        )
        assert self.shift.parse_frosty_filer_path(path) == (
            "ps5",
            "final",
            "digital_combined",
            "patch_combine",
            None,
        )
