# *************************************************************
#  Sets up the initial needs to point to our vSphere server
# *************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# FB1_DEV_NA_PS: fb1_dev_na_ps, check CONTRIBUTING.md before editing here.
# *************************************************************

locals {
  module_settings = {
    # DEDICATED NODES
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps poolbuild statebuild code tool release" }
    "code_tool_release_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps poolbuild statebuild code code-stressbulkbuild" }
    "code_code_stressbulkbuild_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "_wait_for_migration_and_switch_labels_" }

    # poolbuild STATEBUILD CODE
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata win64 final" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_win64_final_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata win64 retail" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_win64_retail_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata win64 performance" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_win64_performance_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata ps5 final" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_ps5_final_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebent uild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata ps5 retail" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_ps5_retail_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata ps5 release" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_ps5_release_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata ps5 performance" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_ps5_performance_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata xbsx final" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_xbsx_final_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata xbsx retail" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_xbsx_retail_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata xbsx release" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_xbsx_release_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata xbsx performance" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_xbsx_performance_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "3", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata linux64" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_linux64_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata server" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_server_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "_wait_for_migration_and_switch_labels_" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps statebuild_dev-na-bfdata poolbuild statebuild poolbuild_dev-na-bfdata linuxserver" }
    "statebuild_dev_na_bfdata_statebuild_poolbuild_dev_na_bfdata_linuxserver_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "_wait_for_migration_and_switch_labels_" }

    # LKG NODES
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps ps lkg_bootanddeploy win64" }
    "ps_lkg_bootanddeploy_win64_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "_wait_for_migration_and_switch_labels_", cpu_core = "5" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps ps lkg_bootanddeploy ps5" }
    "ps_lkg_bootanddeploy_ps5_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "_wait_for_migration_and_switch_labels_", cpu_core = "5" }
    # REPLACE AFTER MIGRATION: labels = "_fb1_dev_na_ps ps lkg_bootanddeploy xbsx" }
    "ps_lkg_bootanddeploy_xbsx_001" = { datastore = "BPS-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "_wait_for_migration_and_switch_labels_", cpu_core = "5" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "fb1-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://fb1-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD-PS")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = var.packer_template
  vsphere_network       = var.network
  vsphere_datacenter    = var.datacenter
  vsphere_folder        = var.vsphere_location
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = var.domain_name
  domain_ou             = var.domain_ou
  hardware_version      = var.hardware_version
  local_admin_group     = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
