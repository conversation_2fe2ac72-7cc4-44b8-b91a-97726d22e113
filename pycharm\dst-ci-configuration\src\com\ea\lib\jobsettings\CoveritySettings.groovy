package com.ea.lib.jobsettings

class CoveritySettings extends JobSetting {
    String nonVirtualCodeBranch
    String nonVirtualCodeFolder
    String coverityCredentials
    String essSecretsCredential
    String essSecretsKey

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = "Sync ${branchInfo.branch_name} code and run Coverity."
        cronTrigger = branchInfo.coverity_settings.trigger ?: 'H/2 * * * 1-6\nH/2 6-23 * * 7'
        nonVirtualCodeBranch = branchInfo.coverity_settings.non_virtual_code_branch ?: ''
        nonVirtualCodeFolder = branchInfo.coverity_settings.non_virtual_code_folder ?: ''
        codeBranch = branchInfo.code_branch
        codeFolder = branchInfo.code_folder
        projectName = projectFile.name
    }

    void initializeCoverity(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.coverity_settings.job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.coverity_settings.timeout_hours ?: 52
        timeoutMinutes = timeoutHours * 60
        coverityCredentials = branchInfo.coverity_settings.credentials
        essSecretsCredential = branchInfo.coverity_settings.ess_secrets_credential
        essSecretsKey = branchInfo.coverity_settings.ess_secrets_key

        String artifactoryCoveritySourcePath = branchInfo.coverity_settings.artifactory_source_path ?:
            'bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2024.12.0.zip'
        extraArgs = branchInfo.coverity_settings.extra_args ?: ''

        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Run Coverity and submit results'

        elipyCmd = "${this.elipyCall} coverity --code-changelist %CODE_CHANGELIST% --code-branch ${branchInfo.code_branch}" +
            ' --artifactory-user %ARTIFACTORY_USER% --artifactory-apikey %ARTIFACTORY_API_KEY% --artifactory-coverity-source-path' +
            " ${artifactoryCoveritySourcePath} --coverity-user %COVERITY_USER% --coverity-password %COVERITY_PASSWORD%" +
            " --clean %CLEAN_LOCAL% --clean-coverity-client %CLEAN_COVERITY_CLIENT% ${extraArgs}"
    }
}
