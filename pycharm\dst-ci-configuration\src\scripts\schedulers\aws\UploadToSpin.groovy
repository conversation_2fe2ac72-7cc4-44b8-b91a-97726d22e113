package scripts.schedulers.aws

import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSpin.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Spin') {
            steps {
                P4SyncDefault(
                    project,
                    branchFile,
                    env.code_folder,
                    env.code_branch,
                    'code',
                    params.code_changelist,
                )
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'submit_to_spin',
                        '--code-branch', env.code_branch,
                        '--code-changelist', params.code_changelist,
                        '--data-branch', env.data_branch,
                        '--data-changelist', params.data_changelist ?: params.code_changelist,
                        '--platform', env.platform,
                        '--format', env.format,
                        '--config', env.config,
                        '--region', env.region,
                    ].join(' '))
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchFile.standard_jobs_settings?.slack_channel_spin
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
