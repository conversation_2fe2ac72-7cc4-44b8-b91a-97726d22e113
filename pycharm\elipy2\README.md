# Execution Layer in Python (ELIPY2) [![pipeline status](https://gitlab.ea.com/dre-cobra/elipy/elipy2/badges/master/pipeline.svg)](https://gitlab.ea.com/dre-cobra/elipy/elipy2/commits/master)

This repository hosts the source of our CI execution layer, ELIPY version 2.

<https://confluence.ea.com/display/DRE/Cobra+ELIPY+Design+Document>

[Visit our Autogenerated API Documentations Page here.](https://dre-cobra.gitlab.ea.com/elipy/elipy2/)

## What is ELIPY

ELIPY (pronounced *ˈilaɪ paɪ*, like the name Eli and the word pie) is a CI framework for use with the Frostbite engine.
It encapsulates a lot of common tasks and uses standard patterns that build teams find themselves performing in the CI/CD flow around Frostbite projects.

## Local development setup

### Prerequisites

- [git](https://git-scm.com/downloads) (Duh!)
- Supported versions of [Python](https://www.python.org/downloads/)
  - 3.7
  - 3.8
  - 3.11

### Bootstrapping your environment

Elipy2 is essentially a Python wrapper around various dependencies, such as P4, Shift, Avalance, fbcli, etc. It can be used as a package
to be imported in other projects in order to abstract that functionality away but it's mostly used as the intermediary layer between
the build scripts and the dependencies.

[elipy-scripts](https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts) contains the elipy CLI and various build scripts that map to specific CLI commands (*e.g., build code, cook data,
upload to the network fileshare, etc*). Both repos are required to be able to run the build scripts.

The ELIPY tech stack (elipy2 with elipy-scripts) has been used in production for several years and has launched multiple
games from DICE and Criterion.

#### Notes

- *Generally, it's easiest to create a new directory (`elipy`) and clone the `elipy2` and `elipy-scripts` repo into it
(e.g. `./elipy/elipy2` and `./elipy/elipy-scripts`) as you can then open the `elipy` directory in an IDE, but this is not a requirement.*

- *Note: to avoid dependency clashes, Elipy uses the version of Python shipped with Frostbite. This adds certain requirements, such as
requiring all Elipy commands to be run from within `fbcli` due to various dependent environment variables.
There are ways around this, however, explained below.*

- *Note: as the `elipy-scripts` repo contains specific instructions for setting up the development environment with Visual Studio Code,
these instructions will focus on using PyCharm (though they are applicable to any development environment).*

- *Note: within `elipy-scripts` the scripts are referred to as `dice_elipy_scripts` but the same scripts are also used to build Criterion games.*

- *Note: most of the examples contain explicit paths for illustrative purposes only. Remember to update these paths to reflect your own development environment.*

1. (optional) Create the main `elipy` directory and enter it. This makes PyCharm support much simpler.

```
mkdir elipy && cd elipy
```

2. Clone the Elipy scripts repository.

```sh
git clone https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts/
```

- Clone the Elipy2 repository.

```sh
git clone https://gitlab.ea.com/dre-cobra/elipy2.git
cd elipy2
```

3. Ensure you have the game code synced (outside the scope of this README, contact a member of the Cobra team for help). Open a ``fbcli`` window
by navigating to the ``...$GAME_ROOT\TnT\Bin\fbcli\cli.bat``, e.g.

```
D:\dev\kin-dev-unverified\TnT\Bin\fbcli\cli.bat
```

4. Set Elipy config path. Each project has a `yml` file specifying various configuration settings. For local development, you can use `.../yml/elipy_dev.yml`,
defined in the `elipy-scripts` repo. There are two options to specify it: environment variables or modifying `./elipy2/setup.cfg`. Environment variables is a little
more tedious as you need to ensure they are always correctly defined in your work environment, but modifying `./elipy2/setup.cfg` can
result in accidentally committing your changes.

   1.

    ```bat
    set ELIPY_CONFIG=C:/Git/elipy-scripts/dice_elipy_scripts/yml/elipy_dev.yml
    ```

   2. In `setup.cfg`, set `ELIPY_CONFIG` to the path to the YAML configuration file:

    ```
    ELIPY_CONFIG=C:/Git/elipy-scripts/dice_elipy_scripts/yml/elipy_dev.yml
    ```

5. Update the ELIPY_CONFIG yml (`elipy-scripts/dice_elipy_scripts/elipy_dev.yml`) to point to the `elipy-scripts` project you are using. In almost all cases,
this will be `dice_elipy_scripts`

```yml
...
script_path:
    ...
    - "C:\\Git\\elipy-scripts\\dice_elipy_scripts"
...
```

6. Create the virtual environment and install the necessary requirements for both ``elipy2`` and ``elipy-scripts``.

```sh
install_elipy.bat elipy_dev.yml dice_elipy_scripts
```

*In case you receive the following error when running the above script, please ignore it for now. It will not interfere with how Elipy works and Elipy will be completley installed.*

```bat
ERROR: Invalid requirement: ''
```

In case you receive the following warning, you can ignore it. It's complaining because of multiple existing versions of
the same package exist in the package location. The install script could delete any packages with the pattern
~ip in the site-packages folder before installing to resolve this, but keeping them will have no negative effect.

```
WARNING: Ignoring invalid distribution -ip (e:\dev\bf-trunk-all\tnt\bin\python\3\lib\site-packages)
```

7. (optional, should be run as part of the the install script) Install ``elipy2`` as a development package. This will allow you to make changes to ``elipy2`` and run them locally. If you skip this setup, the elipy2 package
from the PyPI Artifactory will be used and you will only be able to see changes made in ``elipy-scripts``. This creates
a Python file called ``elipy-script.py`` in the ``Scripts`` virtual environment folder.

```sh
python setup.py develop
```

8. Run unit tests.

```sh
python setup.py test
```

10. Run linting

```sh
python -m pylint elipy2
```

## IntelliJ with Python Plugin / PyCharm Configuration

#### Set sources root

- Mark ``elipy/elipy2`` and ``elipy/elipy-scripts``as *Sources root*. This makes sure PyCharm interprets import paths correctly.

![pycharm-mark-sources-root.png](docs/images/pycharm-mark-sources-root.png)

#### Install PyLint plugin

- Settings -> Plugins -> PyLint
- View -> Tool Windows -> PyLint
- Run from the PyLint menu

![pycharm-pylint.png](docs/images/pycharm-pylint.png)

#### Set project interpreter

1. File -> Settings -> Project: Elipy -> Python Interpreter -> Add Local Interpreter -> Add Existing
2. Navigate to the virtual environment created above and select ``python.exe``, located in the Scripts folder

![add-project-interpreter.png](docs/images/pycharm-add-project-interpreter.png)

### Run Configurations

#### Quickstart

- Copy the ``docs/runConfigurations`` into your ``elipy/.idea`` folder and restart.
- You will need to manually change any defined environment values.

#### Configure unit tests

- Right-click on the tests folder and select ``Run 'pytests in tests``

![pycharm-run-unit-test.png](docs/images/pycharm-run-unit-test.png)

- The tests will run and should all pass. You should then be able to rename this Run Configuration to something
unique and recognisable. You should also click ``Save configuration``.

![pycharm-runtime-configuration_1.png](docs/images/pycharm-runtime-configuration-1.png)

![pycharm-runtime-configuration_2.png](docs/images/pycharm-runtime-configuration-2.png)

- To be able to run unit tests with the debugger, you need to include the flag ``--no-cov`` in Additional Arguments.

![pycharm-run-unit-test-2.png](pycharm-run-unit-test-2.png)

#### Run elipy with debugger

By providing the specific elipy innovation script along with the fbcli path, we can bootstrap our way into tricking
PyCharm to run elipy in its environment as if it were in a Frostbite one.

1. Right-click on ``run_elipy_local.py`` and select Run or Debug

![pycharm-elipy-debug-1.png](docs/images/pycharm-elipy-debug-1.png)

2. This will fail as you need to include some environment variables (but it will generate the Run Configuration).
Include the environment variables as defined in ``run_elipy_local.py`` - specifically, the path to the fbcli and
the elipy-script.py script.

![pycharm-elipy-debug-2.png](docs/images/pycharm-elipy-debug-2.png)

![pycharm-elipy-debug-3.png](docs/images/pycharm-elipy-debug-4.png)

3. Now run the script again and you should see elipy has been correctly called!

![pycharm-elipy-debug-4.png](docs/images/pycharm-elipy-debug-4.png)

4. You can also pass in additional arguments to elipy via the Parameters field in the Run Configuration.

![pycharm-elipy-debug-5.png](docs/images/pycharm-elipy-debug-5.png)

![pycharm-elipy-debug-6.png](docs/images/pycharm-elipy-debug-6.png)

---

# FAQ & Troubleshooting

## How do I sync and build the game?

### BCT

Follow instructions outlined [here](https://confluence.ea.com/pages/viewpage.action?pageId=954175768).

### Excalibur

TODO.

## Why version 2

We use the namespace elipy2 to avoid the existing elipy module so it could be easier to migrate to the new version.

## Prerequisites and Dependencies

We currently have a hard dependency on the Bilbo API which is stored in perforce.

- Frostbite engine version >= *2016.4*.
- Frostbite environment is setup locally, normally run *TnT\Bin\fbcli\cli.bat*.

## Configure ELIPY Config Path

When using ELIPY, you need to set the following environment variable `ELIPY_CONFIG` to the path of your ELIPY config file. By default, ELIPY will use [`%TNT_ROOT%\build\bin\elipy\elipy.yml`](https://gitlab.ea.com/dre-cobra/elipy2/blob/f2ed8351ce9817118800af3c894ba57c4973a777/elipy2/config.py#L45).

You can also specify your own path; this can an [absolute path](https://gitlab.ea.com/dre-cobra/elipy2/blob/f2ed8351ce9817118800af3c894ba57c4973a777/elipy2/config.py#L54), or a [relative path](https://gitlab.ea.com/dre-cobra/elipy2/blob/f2ed8351ce9817118800af3c894ba57c4973a777/elipy2/config.py#L54) from `GAME_ROOT`:

```batch
set ELIPY_CONFIG=tnt\Build\Bin\Elipy\elipy_criterion.yml
set ELIPY_CONFIG=D:\j\w\game\tnt\Build\Bin\Elipy\elipy_criterion.yml
```

This should to be done for both local development and autobuilds.

## Adding ELIPY to your project

Elipy needs to run in a virtual python environment to avoid conflicts with the fbenv python packages checked into perforce. The virtual environment needs to be created with the --system-site-packages flag so that fbenv can still find all it's dependencies.

- Easiest way to set this up is to use a version of the `install_elipy.bat` file and call:

```batch
TnT\Bin\fbcli\cli.bat && install_elipy.bat
```

The `install_elipy.bat` file in this repo is meant for setting up your environment for local development so it does not install the elipy package. For an example of a file used in a build farm see this repository <https://gitlab.ea.com/dre-cobra/elipy/elipy-setup/blob/master/install-elipy.bat>

- The elipy package is installed with pip with the following call:

```batch
python -m pip install -i https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple --upgrade elipy2
```

- If you are uploading your build scripts as a python package, you can add that to the `install_elipy.bat` file as well.
- Setting the `ELIPY_CONFIG` value in the `install_elipy.bat` file is also a convenient option.

You can now invoke a ELIPY command using the following command:

```batch
"D:\J\w\Game\tnt\bin\fbcli\cli.bat" x64 && install_elipy.bat && elipy command arguments
```

Example:

```batch
"D:\J\w\Game\tnt\bin\fbcli\cli.bat" x64 && install_elipy.bat && elipy codebuild_example xb1 final --branch game-dev-dakar --changelist 4592500 --clean false --dryrun
```

## Configuring your ELIPY config

The ELIPY config file contains a significant number of configurable settings. Only a few settings are required, but what you need to configure will change from script to script and depending on the arguments you pass into those scripts.

You can find more details about each setting [here](./ELIPY_CONFIG.md)

## Studio/project specific scripts

In the scripts folder, `elipy2\scripts`, we keep example scripts that allows anyone to install elipy to run some basic tasks. They can also be used as examples of how to write scripts. Scripts can be kept anywhere but for elipy to find them the path to the script location needs to be added to the ELIPY config file. Multiple paths can be added like:

```batch
script_path:
    - "TnT\\Bin\\Python\\3\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "D:\\git\\repo\\scripts"
```

Paths later in the list have priority. So if scripts in different locations have the same names the one found first will be used and the list is iteration starts with the last path in the list. In this example scripts in the git\\repo location would have priority since that is last in this list. The scripts in the elipy2 repo are added as a last option, so they have the lowest priority in case of recurring script names.

## Debugging ELIPY2 via Visual Studio Code

Our IDE of choice is [Visual Studio Code](https://code.visualstudio.com/) with the [Python extension](https://code.visualstudio.com/docs/python/python-tutorial) enabled. Configuring other IDEs is beyond scope of this guide.

To set up debugging via VS Code:

- Open up the VS Code command palette (CTRL+SHIFT+P)
- Type `>launch.json`
- Edit the first configuration object, i.e. this would allow you to debug the `codebuild` command/script:

```json
        {
            "name": "Python",
            "type": "python",
            "request": "launch",
            "stopOnEntry": true,
            "pythonPath": "${config:python.pythonPath}",
            "program": "${env:TNT_ROOT}\\bin\\python\\2.7\\scripts\\elipy-script.py",
            "args": [
                "codebuild",
                "tool",
                "release",
                "--branch",
                "game-dev",
                "--changelist",
                "1337"
            ],
            "cwd": "${workspaceFolder}",
            "env": {"PYTHONPATH": "${env:PYTHONPATH}"},
            "envFile": "${workspaceFolder}/.env",
            "debugOptions": [
                "RedirectOutput"
            ]
        },
```

If you're debugging some other command you'll need to update the `args` section.

After adding the above json to your launch.json, you will need to make sure that you have selected the `Python` debug configuration on the Debug side panel (Ctrl+Shift+D).

## Publish

### **ONLY FOR DEPLOYMENT, YOU DON´T NEED THIS LOCALLY**

- You need to have the `$HOME/.pypirc` or `%USERPROFILE%\.pypirc` available to publish the package to internal PyPi server:

* **Prod**: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated

```batch
[distutils]
index-servers = https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated

[artifactory]
repository = https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
username = <EMAIL>
password = <password - erpm.ea.com>
```

- And you should NOT run following command locally.

```batch
python setup.py bdist_wheel upload -r https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
```

For more details about Artifactory's PyPi servers, please see: <https://www.jfrog.com/confluence/display/RTF/PyPI+Repositories>

## elipy_example.yml

### **ONLY USED FOR UNITTESTS DEVELOPMENT**

- Example options are added to the elipy_example.yml file, here elipy will grab project specific settings to run a job, such as avalanche.

```batch

  avalanche:
    avalanche_size: 150 #Size of the avalanche store in GB
    propagate_gets: true #true or false, GET's from upstream
    expiration_time_in_day: 3 #Expiration time in days 3 = never
    #never, monday, tuesday etc
    defrag_day: 'never' #What day a defrag should be done (off so we decide)
    full_defrag_day: 'never' #What day a full defrag should be done (off so we decide)
    maintenance_time_of_day : 9999 #Next maintenance time of day in minutes, set to 9999 so it never runs so only we trigger it.
    maintenance_window_in_minutes : 90 #Maintenance window in minutes.

```

- changing the various options will change how avalanche is setup when maintenance cycles are ran on the build farm.

- There are a host of options that can be edited to fit the project so its highly recommned you check out elipy_example.yml and see what is avaliable.

### **PRODUCTION**

- The production elipy.yml file is what is used by the build farm when running which is located in perforce, typically //Casablanca/Code/TnT/Build/Bin/Elipy/elipy.yml.

- Production and the example .yml files should be a carbon copy of each other so any anomalies can be found in development before it reaches production.

## Documentation-in-Code and Docstrings

This repository relies on the sphinx documentation engine to generate the documentation. Sphinx uses the reStructuredText-style Python docstrings to format its response.

Here is an example of what can be included in a function/class:

```python
def foo(arg1, arg2):
  """
  Insert description here.

  :param arg1: description
  :param arg2: description
  :type arg1: type description
  :type arg1: type description
  :return: return description
  :rtype: the return type description
  """

  result = arg1 + arg2

  return result
```
