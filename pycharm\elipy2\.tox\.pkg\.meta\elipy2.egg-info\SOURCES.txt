README.md
setup.cfg
setup.py
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/PKG-INFO
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/SOURCES.txt
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/dependency_links.txt
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/entry_points.txt
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/not-zip-safe
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/requires.txt
C:/Users/<USER>/vscode/pycharm/elipy2/.tox/.pkg/.meta/elipy2.egg-info/top_level.txt
elipy2/__init__.py
elipy2/artifactory_client.py
elipy2/avalanche.py
elipy2/aws.py
elipy2/az_utils.py
elipy2/azcopy_client.py
elipy2/bilbo.py
elipy2/bilbo_v2.py
elipy2/build_metadata.py
elipy2/build_metadata_utils.py
elipy2/cli.py
elipy2/code.py
elipy2/command_logger.py
elipy2/config.py
elipy2/core.py
elipy2/custom_logging.py
elipy2/data.py
elipy2/denuvo.py
elipy2/elipy_example.yml
elipy2/elipy_example_shift.yml
elipy2/elipy_steam_user_mapping_example.yml
elipy2/enums_utils.py
elipy2/exceptions.py
elipy2/expire.py
elipy2/filer.py
elipy2/filer_paths.py
elipy2/frostbite_core.py
elipy2/local_paths.py
elipy2/multiprocessing.py
elipy2/oreans.py
elipy2/p4.py
elipy2/package.py
elipy2/reqs_utils.py
elipy2/retry_utils.py
elipy2/run.py
elipy2/running_processes.py
elipy2/secrets.py
elipy2/shift_utils.py
elipy2/shifters.py
elipy2/steam_utils.py
elipy2/symbols.py
elipy2/telemetry.py
elipy2/vault.py
elipy2/windows_tools.py
elipy2/avalanche_web_api/cache.py
elipy2/avalanche_web_api/core.py
elipy2/avalanche_web_api/database.py
elipy2/avalanche_web_api/server.py
elipy2/avalanche_web_api/storage.py
elipy2/frostbite/__init__.py
elipy2/frostbite/build_agent_utils.py
elipy2/frostbite/fbcli.py
elipy2/frostbite/fbenv_layer.py
elipy2/frostbite/icepick.py
elipy2/frostbite/licensee_utils.py
elipy2/frostbite/package_utils.py
elipy2/frostbite/sdk_utils.py
elipy2/frostbite/resources/ThresholdCleanupNode.ps1
elipy2/scripts/__init__.py
elipy2/scripts/codebuild_example.py
elipy2/scripts/codepreflight_example.py
elipy2/scripts/databuild_example.py
elipy2/scripts/frosty_example.py
elipy2/scripts/icepick_example.py
elipy2/scripts/import_data_example.py
elipy2/scripts/info.py
elipy2/scripts/patch_databuild_example.py
elipy2/scripts/patch_frosty_example.py