# This is a simple configuration file
## it sets configuration-specific variables
## in this example config file, we have the following configs:
## - default
## - criterion
## - production
## and so on.
## These configurations are simple dicts, and no assumptions or DSL is honoured
## For more information see [config.py](elipy2/config.py)

# Mandatory
default:
  avalanche_symbol_server: 'http://dice-wal-sym.test.com:1338'

shift_tests:
  avalanche_symbol_server: 'http://dice-wal-sym.test.com:1338'
  build_share: '\\filer.test\builds\DICE'
  bilbo_url: "http://bilbo-es-test.test.se"
  bilbo_api_version: 2
  shift_retention: 36
  shift_submission_path: "\\\\filer.test\\builds\\Shift\\auto_submissions"
  shift_tool_url: "https://shift_tool_url.ea.com/artifactory/list/dre-generic-federated/shiftsubmission/4.0.1"
  shift_config_file: "elipy_example_shift.yml"

# Mandatory
production:
  avalanche_symbol_server: 'http://dice-wal-sym.test:1338'

write_cmd_log_tests:
  enable_executed_command_log: true

write_cmd_log_tests_false:
  enable_executed_command_log: false
