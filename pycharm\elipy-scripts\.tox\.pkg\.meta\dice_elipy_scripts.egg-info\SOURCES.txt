README.md
setup.cfg
setup.py
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/PKG-INFO
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/SOURCES.txt
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/dependency_links.txt
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/not-zip-safe
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/requires.txt
C:/Users/<USER>/vscode/pycharm/elipy-scripts/.tox/.pkg/.meta/dice_elipy_scripts.egg-info/top_level.txt
dice_elipy_scripts/__init__.py
dice_elipy_scripts/antifreeze.py
dice_elipy_scripts/avalanche_maintenance.py
dice_elipy_scripts/avalanchecli_drop_all_dbs.py
dice_elipy_scripts/avalanchecli_nuke.py
dice_elipy_scripts/backup_baseline.py
dice_elipy_scripts/backup_local.py
dice_elipy_scripts/bake_flux.py
dice_elipy_scripts/bilbo_register_autotest.py
dice_elipy_scripts/bilbo_register_build.py
dice_elipy_scripts/bilbo_register_drone.py
dice_elipy_scripts/bilbo_register_frosty.py
dice_elipy_scripts/bilbo_register_release_candidate.py
dice_elipy_scripts/bilbo_register_smoke.py
dice_elipy_scripts/bilbo_register_verified_for_preflight.py
dice_elipy_scripts/bilbo_select_autotest.py
dice_elipy_scripts/clean_agent.py
dice_elipy_scripts/clean_icepick_logs.py
dice_elipy_scripts/codebuild.py
dice_elipy_scripts/codecopy.py
dice_elipy_scripts/codecoverage.py
dice_elipy_scripts/codepreflight.py
dice_elipy_scripts/combined_bundles.py
dice_elipy_scripts/copy_from_filer_to_azure.py
dice_elipy_scripts/copy_integrate_compile.py
dice_elipy_scripts/coverity.py
dice_elipy_scripts/custom_script.py
dice_elipy_scripts/data_upgrade.py
dice_elipy_scripts/data_upgrade_integration.py
dice_elipy_scripts/data_upgrade_preflight.py
dice_elipy_scripts/data_upgrader_validator.py
dice_elipy_scripts/databuild.py
dice_elipy_scripts/datapreflight.py
dice_elipy_scripts/datasnooper.py
dice_elipy_scripts/delete_git_lock_file.py
dice_elipy_scripts/deleter.py
dice_elipy_scripts/frosty.py
dice_elipy_scripts/icepick_run.py
dice_elipy_scripts/icepick_run_codetests.py
dice_elipy_scripts/icepick_run_preflight.py
dice_elipy_scripts/icepick_warm_cache.py
dice_elipy_scripts/imerge.py
dice_elipy_scripts/info.py
dice_elipy_scripts/integrate.py
dice_elipy_scripts/integrate_compile_upgrade_cook.py
dice_elipy_scripts/integrate_upgrade_one_stream.py
dice_elipy_scripts/kill_processes.py
dice_elipy_scripts/move_location_bundles.py
dice_elipy_scripts/move_location_drone.py
dice_elipy_scripts/move_location_frosty.py
dice_elipy_scripts/nant.py
dice_elipy_scripts/navmesh.py
dice_elipy_scripts/offsitebuild.py
dice_elipy_scripts/outsource_package.py
dice_elipy_scripts/p4_clean.py
dice_elipy_scripts/p4_copy.py
dice_elipy_scripts/p4_copy_cherrypick.py
dice_elipy_scripts/p4_copy_data_upgrade.py
dice_elipy_scripts/p4_counter.py
dice_elipy_scripts/p4_delete_workspace.py
dice_elipy_scripts/p4_dvcs.py
dice_elipy_scripts/patch_databuild.py
dice_elipy_scripts/patch_frosty.py
dice_elipy_scripts/pipeline_determinism_test.py
dice_elipy_scripts/pipeline_warning.py
dice_elipy_scripts/post_clean.py
dice_elipy_scripts/post_preflight.py
dice_elipy_scripts/pre_preflight.py
dice_elipy_scripts/prebuild.py
dice_elipy_scripts/preflight_submit.py
dice_elipy_scripts/prepare_vm.py
dice_elipy_scripts/process_shift_subscription_downloads.py
dice_elipy_scripts/quickscope_import.py
dice_elipy_scripts/register_outage_metric.py
dice_elipy_scripts/smoke_integrate.py
dice_elipy_scripts/sparta_bundle.py
dice_elipy_scripts/submit_to_shift.py
dice_elipy_scripts/submit_to_spin.py
dice_elipy_scripts/symbol_store_upload.py
dice_elipy_scripts/test_nant.py
dice_elipy_scripts/test_runner.py
dice_elipy_scripts/trigger_docker_image_gitlab_pipeline.py
dice_elipy_scripts/unittests.py
dice_elipy_scripts/upload_to_steam.py
dice_elipy_scripts/vault.py
dice_elipy_scripts/virtualize.py
dice_elipy_scripts/webexport.py
dice_elipy_scripts/gametool/__init__.py
dice_elipy_scripts/gametool/drone.py
dice_elipy_scripts/gametool/fbenv.py
dice_elipy_scripts/gametool/framework.py
dice_elipy_scripts/gametool/frostbite_database_upgrader.py
dice_elipy_scripts/gametool/frostyisotool.py
dice_elipy_scripts/gametool/gametool.py
dice_elipy_scripts/utils/__init__.py
dice_elipy_scripts/utils/autotest_utils.py
dice_elipy_scripts/utils/azure_filer_utils.py
dice_elipy_scripts/utils/bilbo_utils.py
dice_elipy_scripts/utils/code_utils.py
dice_elipy_scripts/utils/data_build_utils.py
dice_elipy_scripts/utils/datapreflight_utils.py
dice_elipy_scripts/utils/dbxmerge.py
dice_elipy_scripts/utils/decorators.py
dice_elipy_scripts/utils/delete_utils.py
dice_elipy_scripts/utils/env_utils.py
dice_elipy_scripts/utils/file_system.py
dice_elipy_scripts/utils/frosty_build_utils.py
dice_elipy_scripts/utils/gamescripts_utils.py
dice_elipy_scripts/utils/icepick_utils.py
dice_elipy_scripts/utils/integration_utils.py
dice_elipy_scripts/utils/licensee_helper.py
dice_elipy_scripts/utils/p4_utils.py
dice_elipy_scripts/utils/preflight_utils.py
dice_elipy_scripts/utils/sentry_utils.py
dice_elipy_scripts/utils/snowcache_utils.py
dice_elipy_scripts/utils/state_utils.py
dice_elipy_scripts/utils/sven_utils.py
dice_elipy_scripts/yml/elipy_bct.yml
dice_elipy_scripts/yml/elipy_dev.yml
dice_elipy_scripts/yml/elipy_excalibur.yml
dice_elipy_scripts/yml/elipy_fb1.yml
dice_elipy_scripts/yml/elipy_fc_ml.yml
dice_elipy_scripts/yml/elipy_fifa_skybuild.yml
dice_elipy_scripts/yml/elipy_frostbite.yml
dice_elipy_scripts/yml/elipy_granite.yml
dice_elipy_scripts/yml/elipy_integra.yml
dice_elipy_scripts/yml/elipy_kingston.yml
dice_elipy_scripts/yml/elipy_merlin.yml
dice_elipy_scripts/yml/elipy_nfsupgrade.yml
dice_elipy_scripts/yml/elipy_sportsatg.yml
dice_elipy_scripts/yml/shift_config_bct.yml
dice_elipy_scripts/yml/shift_config_exc.yml
dice_elipy_scripts/yml/shift_config_fb1.yml
dice_elipy_scripts/yml/shift_config_granite.yml
dice_elipy_scripts/yml/shift_config_gu1.yml
dice_elipy_scripts/yml/shift_config_kingston.yml
dice_elipy_scripts/yml/shift_config_merlin.yml
dice_elipy_scripts/yml/shift_config_nfsupgrade.yml
dice_elipy_scripts/yml/shift_config_santiago.yml
dice_elipy_scripts/yml/steam_user_mapping.yml
dice_elipy_scripts/yml/vault_verification_config_bct.yml
dice_elipy_scripts/yml/vault_verification_config_bct_bflabs.yml
dice_elipy_scripts/yml/vault_verification_config_bct_event.yml
dice_elipy_scripts/yml/vault_verification_config_excalibur.yml
dice_elipy_scripts/yml/vault_verification_config_granite.yml
dice_elipy_scripts/yml/vault_verification_config_kingston.yml
dice_elipy_scripts/yml/vault_verification_config_merlin.yml
dice_elipy_scripts/yml/vault_verification_config_nfsupgrade.yml