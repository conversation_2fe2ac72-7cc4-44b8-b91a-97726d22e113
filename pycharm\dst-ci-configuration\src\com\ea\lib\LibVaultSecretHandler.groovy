package com.ea.lib

/**
 * Handles configuration and management of HashiCorp Vault secrets for Jenkins jobs.
 * This class provides functionality to validate, merge, and attach Vault secrets
 * then generate the necessary DSL for Jenkins pipelines vaultBuildWrapper endpoint provided
 * by the Jenkins HashiCorp Vault plugin.
 */
class LibVaultSecretHandler {

    Map vault_config = [:]
    List<Map> vault_secrets = []

    /**
     * Creates a new LibVaultSecretHandler instance
     */
    LibVaultSecretHandler(Map vault_config = [:]) {
        '''
        The vault_config parameter is predefined to use the Vault sidecar documented
        here https://go.ea.com/CobraVaultSidecarDoc
        '''
        this.vault_config = vault_config.isEmpty() ? [
            engineVersion    : 2,
            failIfNotFound   : true,
            timeout          : 60,
            vaultCredentialId: 'vault-auth-dummy',
            vaultUrl         : 'http://127.0.0.1:8200',
        ] : vault_config
    }

    /**
     * Validates that a provided secret contains all required fields.
     * @param secret Object to validate as a secret configuration
     * @return true if the secret is valid
     * @throws IllegalArgumentException if the secret is invalid or missing required fields
     */
    static boolean isValidVaultSecret(Object secret) {
        if (!(secret instanceof Map)) {
            throw new IllegalArgumentException('A vault secret must be a map and isValidVaultSecret validates individual secrets, not lists of secrets.')
        }
        if (!secret.containsKey('vault_secret_path') || !secret.containsKey('target_env_var') || !secret.containsKey('vault_secret_key')) {
            throw new IllegalArgumentException('Vault secret must contain "vault_secret_path", "target_env_var", and "vault_secret_key"')
        }
        return true
    }

    static boolean throwIfTargetEnvVarsAreNotUnique(List<Map> vault_secrets) {
        List<String> targetVariableNames = []
        for (secret in vault_secrets) {
            targetVariableNames << secret['target_env_var']
        }
        Map<String, Boolean> seen = [:]
        List<String> duplicates = []
        for (name in targetVariableNames) {
            if (seen[name]) {
                duplicates << name
            } else {
                seen[name] = true
            }
        }

        if (duplicates) {
            throw new IllegalArgumentException("Vault secrets must have unique target_env_var names. Duplicated variables: ${duplicates.join(', ')}")
        }

        return true
    }

    /**
     * Merges provided vault secrets, grouping them by path.
     * Multiple secrets with the same path are consolidated into a single entry
     * with multiple secret values.
     * @param vault_secrets List of vault secrets to merge
     * @return List of merged vault secrets
     */
    static List<Map> mergeVaultSecrets(List<Map> vault_secrets) {
        for (secret in vault_secrets) {
            isValidVaultSecret(secret)
        }
        throwIfTargetEnvVarsAreNotUnique(vault_secrets)

        Map<String, Map> merged_secrets_map = [:]

        for (secret in vault_secrets) {
            String path = secret['vault_secret_path']
            if (!merged_secrets_map.containsKey(path)) {
                merged_secrets_map[path] = [
                    path        : path,
                    secretValues: [],
                ]
            }
            merged_secrets_map[path]['secretValues'] << [
                envVar    : secret['target_env_var'],
                vaultKey  : secret['vault_secret_key'],
                isRequired: true,
            ]
        }

        List<Map> mergedResults = []
        for (entry in merged_secrets_map.values()) {
            mergedResults << entry
        }
        return mergedResults
    }

    Map getVaultConfiguration() {
        return [
            configuration: this.vault_config,
            vaultSecrets: this.vault_secrets,
        ]
    }

    /**
     * Generates the complete Vault configuration DSL
     * @return A closure containing the complete Vault configuration in DSL format
     */
    Closure getVaultConfigurationDSL() {
        return {
            configuration(mapToDSL(this.vault_config))
            vaultSecrets {
                this.vault_secrets.each { secret ->
                    vaultSecret(secretMapToDSL(secret))
                }
            }
        }
    }

    /**
     * Converts a map to DSL closure format
     * @param map The map to convert to DSL format
     * @return A closure that can be used in DSL context
     */
    private static Closure mapToDSL(Map map) {
        return {
            map.each { key, value ->
                if (value != null) {
                    "$key"(value)  // Remove delegate. and use direct method call
                }
            }
        }
    }

    /**
     * Converts vault secret maps to DSL format
     * @param secretMap The secret map to convert
     * @return A closure that can be used in DSL context
     */
    private static Closure secretMapToDSL(Map secretMap) {
        return {
            path(secretMap.path)
            secretValues {
                secretMap.secretValues.each { value ->
                    vaultSecretValue {
                        envVar(value.envVar)
                        vaultKey(value.vaultKey)
                    }
                }
            }
        }
    }
}
