# Welcome to ELIPY contribution guidelines

## New contributor guide

To get an overview of the project, read the [README](README.md). Here are some resources to help you get started:

- [GitHub Flow](https://docs.github.com/en/get-started/quickstart/github-flow)
- [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)
- [SemVer](https://semver.org/)
- [Black](https://pypi.org/project/black/)
- [EditorConfig](https://editorconfig.org/)
- [Pytest](https://docs.pytest.org/en/7.3.x/)
- [Click](https://click.palletsprojects.com/en/7.x/)
- [GitLab CI quick start](https://docs.gitlab.com/ee/ci/quick_start/)

## Getting started

### Tools & Frameworks

This project uses a selection of tools & frameworks to help improve the developer experience. You should familiarise yourself with these tools before making significant changes:

#### [Pytest](https://docs.pytest.org/en/7.3.x/)

This project uses pytest as it primary testing framework (although there are a lot of legacy tests using the [unittest](https://docs.python.org/3/library/unittest.html) framework). We aim for high test coverage and encourage/require that all changes have supporting tests.

#### [SemVer](https://semver.org/)

When making any release, we do our best to follow SemVer so our users know when to expect breaking changes.

#### [Black](https://pypi.org/project/black/)

Black is a tool for formatting Python code. To format your code, simply run:

```cmd
pip3 install -i https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple -r requirements.txt
python -m black -l 100 .
```

#### [EditorConfig](https://editorconfig.org/)

EditorConfig allows use to specify some file format settings that most IDEs honor. This is to help keep our files consistent across multiple operating systems and improve the developer experience.

#### [lefthook](https://github.com/evilmartians/lefthook#readme)

This is a tool that lets us setup git hooks and them from within the repository. To install the githooks, run the following commands:

```commandline
go install github.com/evilmartians/lefthook@latest
lefthook install
```

### Issues

#### Raise an issue

For now, if you have an issue you want to raise, please mention it in the [#dre-elipy](https://electronic-arts.slack.com/archives/CDUGBRGSF) channel in Slack and tag @dre.elipy.engineers

### Make Changes

We follow [GitHub Flow](https://docs.github.com/en/get-started/quickstart/github-flow) - you should follow this process when making changes.

All changes on development branches are published to an internal PyPi server on Artifactory. If you need to do some further testing external systems you can install your package from there.

### Breaking Changes

This project is used to build several games and as such we should be very mindful about introducing breaking changes. Ideally we should follow the following pattern:

- Deprecate function if needed, using the `@deprecated(...)` decorator
- Check usage of functions or parameters to understand the impact of the change.
  - This only applies to function that have been decorated with `@collect_metrics(...)`
  - [Metrics data](https://dre-metrics-eck.cobra.dre.ea.com/elipy_metrics_*/_search)
  - [Legacy data](http://elipy-telemetry.dre.dice.se/metrics_*/_search)
  - Install <https://elasticvue.com/> in your browser (makes working with ES instance easier)
  - Here, you can search for what functions have been called, when they were called, and what arguments they were called with
    - We remove sensitive information before pushing to ES
- Introduce a backwards compatible change and bump minor version number
- Work with teams to remove usage of the deprecated functions/parameters
- Check usage again
- Remove deprecated functions/parameters or introduce breaking changes and bump the major version

### Merge Request

When you're finished with the changes, create a merge request, also known as a MR.

- Fill the "Ready for review" template so that we can review your MR. This template helps reviewers understand your changes as well as the purpose of your pull request.
- Don't forget to link your MR to an issue.
- We may ask for changes to be made before a MR can be merged, either using [suggested changes](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/incorporating-feedback-in-your-pull-request) or merge request comments. You can apply suggested changes directly through the UI. You can make any other changes in your branch.
- As you update your MR and apply changes, mark each conversation as [resolved](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/commenting-on-a-pull-request#resolving-conversations).
- If you run into any merge issues, checkout this [git tutorial](https://github.com/skills/resolve-merge-conflicts) to help you resolve merge conflicts and other issues.

#### MR Message Requirements

source: <https://docs.gdw.ea.com/skybuild/developer/standards/mr-message-standards>

- 1/8: 📝 Be Clear and Concise:
A good commit message should be clear and concise, summarizing the changes made in the commit. Avoid vague or generic messages like “Fixed a bug” or “Updated code.” Instead, provide specific details that describe the purpose or impact of the changes.

- 2/8: 📖 Include Relevant Context:
It’s essential to include relevant context in your commit messages. Mention any relevant issue numbers, feature requests, or discussions related to the changes. This helps others understand the motivation behind the commit and facilitates collaboration.

- 3/8: 🐛 Mention Bug Fixes:
If your commit addresses a bug or issue, make sure to reference it in the commit message. Include a brief description of the problem and how your changes fix it. This helps track the resolution of bugs and improves the overall project understanding.

- 4/8: 🗂 Organize Commits logically:
If you’re working on a larger feature or fixing multiple issues, consider breaking your changes into smaller, logical commits. Each commit should represent a self-contained unit of work that makes sense on its own. This makes it easier to review, revert, or cherry-pick specific commits.

- 5/8: 📝 Conventional Commits:
Write commit messages in a format that aligns with the Conventional Commit specification. This will help to align on a standard format for commit messages as well as support automation efforts in the future.

- 6/8: 🧪 Test and Proofread:
Before committing your changes, double-check them for errors and test them thoroughly. Ensure that your changes adhere to the project’s coding standards and conventions. Additionally, proofread your commit message to ensure it is free from typos or grammatical errors.

- 7/8: 📚 Follow a Template:
Establish a commit message template for your team or project to maintain consistency. This could include sections like "Summary," "Description," "Testing," or "References." Templates help standardize commit messages and provide a structure for information.

- 8/8: 🔄 Update Commit Messages:
If you need to update a commit message, use Git's --amend option instead of adding a new commit. This avoids cluttering the commit history with multiple messages and ensures that the commit remains self-contained.

### Your MR is merged and released

All commits are release after being successfully merged into `master` and a successful pipeline has been ran.
