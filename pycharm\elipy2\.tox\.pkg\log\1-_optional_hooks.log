name: .pkg
run_id: _optional_hooks
env APPDATA: C:\Users\<USER>\AppData\Roaming
env COMSPEC: C:\WINDOWS\system32\cmd.exe
env LANG: en_US.UTF-8
env NUMBER_OF_PROCESSORS: 14
env PATH: C:\Users\<USER>\vscode\pycharm\elipy2\.tox\.pkg\Scripts;D:\vscode\elipy_venv\Scripts;C:\Program Files\Alacritty\;C:\Program Files\Python311\Scripts\;C:\Program Files\Python311\;C:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\Program Files\Python312\Scripts\;C:\Program Files\Python312\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\Perforce\;C:\Program Files\Git\cmd;C:\Program Files\OpenJDK\jdk-17.0.2\bin;C:\Program Files\Microsoft\jdk-********-hotspot\\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin
env PATHEXT: .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL
env PIP_DISABLE_PIP_VERSION_CHECK: 1
env PROCESSOR_ARCHITECTURE: AMD64
env PROGRAMDATA: C:\ProgramData
env PROGRAMFILES: C:\Program Files
env PROGRAMFILES(X86): C:\Program Files (x86)
env PYTHONHASHSEED: 887
env PYTHONIOENCODING: utf-8
env SYSTEMDRIVE: C:
env SYSTEMROOT: C:\WINDOWS
env TEMP: C:\Users\<USER>\AppData\Local\Temp\1
env TMP: C:\Users\<USER>\AppData\Local\Temp\1
env TOX_ENV_DIR: C:\Users\<USER>\vscode\pycharm\elipy2\.tox\.pkg
env TOX_ENV_NAME: .pkg
env TOX_WORK_DIR: C:\Users\<USER>\vscode\pycharm\elipy2\.tox
env USERPROFILE: C:\Users\<USER>\Users\hvu\vscode\pycharm\elipy2\.tox\.pkg
env WINDIR: C:\WINDOWS
metadata pid: 2968
cwd: C:\Users\<USER>\vscode\pycharm\elipy2
allow: C:\Users\<USER>\vscode\pycharm\elipy2\.tox\.pkg\Scripts\*
cmd: python "C:\Program Files\Python311\Lib\site-packages\pyproject_api\_backend.py" True setuptools.build_meta __legacy__
exit_code: None
started backend BackendProxy(backend=<setuptools.build_meta._BuildMetaLegacyBackend object at 0x0000025BD75F8190>)
