# ELIPY Config

## Info

Each item in this document will contain the following:

- Title/Name
- Type information
- If it is required or not
  - Some settings will be required depending on what scripts you are running or what settings you have passed in.
- Example configuration
- Description

## Location `default`

A location in the ELIPY config file is a key-value-part that you can find at the root for the file. The `default` value is mandatory but you can create as many locations as you want.

Each additional location you create, you can override the settings found in the `default` location.

## script_path

- *type*: list
- *required*: True
- *example*:

```yaml
default:
    script_path:
        - "D:\\gitLab\\elipy-scripts\\dice_elipy_scripts"
```

- *description*: where can you find the ELIPY scripts.

## build_share

- *type*: string
- *required*: True
- *example*:

```yaml
default:
    build_share: "\\\\my_studio_share\\builds\\Project"
```

- *description*: What is the root path of the networkshare where builds will be stored

## avalanche_state_lifetime

- *type*: int
- *required*: False
- *example*:

```yaml
default:
    avalanche_state_lifetime: 5
```

- *description*:

## avalanche_state_host

- *type*: list[dict]
- *required*: False
- *example*:

```yaml
default:
    avalanche_state_host:
        xb1: "my_avalanche_state_host.dre.ea.com"
```

- *description*: What are the avalanche state hosts for each platform

## bilbo_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    bilbo_url: "https://my_bilbo_server.dre.ea.com"
```

- *description*: What is the Biblo server that you want to push data to

## metadata_manager

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
    metadata_manager:
    primary:
        name: "bilbo_v2"
        url: "https://my_bilbo_v2_server.dre.ea.com"
        attributes_filename: "build.json"
    secondary:
        - name: "bilbo"
        index: "bilbo_v1"
        url: "https://my_bilbo_v1_server.dre.ea.com"
        attributes_filename: "bilbo_v1.json"
```

- *description*: Metadata provider settings

## enable_executed_command_log

- *type*: bool
- *required*: False
- *example*:

```yaml
default:
    enable_executed_command_log: true
```

- *description*: If ELIPY should we generate a log of all the commands we shell out to

## licensee_code_folder_name

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    licensee_code_folder_name: "Code\\DICE"
```

- *description*: Where to find the licensee path relative from TNT

## md5_exf_path

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    md5_exf_path: "Build\\Jenkins\\Common\\Scripts\\Util\\exf.exe"
different_location:
    md5_exf_path: "C:\\dre\\bin\\exf\\exf.exe"
```

- *description*: Where to find the md5 hashing tool either absolute path or relative from TNT

## handle_exe_path

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    handle_exe_path: C:\\ProgramData\\chocolatey\\bin\\handle
```

- *description*: Path to the handle.exe

## filer_api_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    filer_api_url: "https://it-sweden-api.dice.se/api/v1/smb"
```

- *description*: Fileshare api endpoint for closing handles. Currently only available in Stockholm

## denuvo

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
    denuvo:
        artifactory_path: "https://artifacts.ea.com/artifactory/dre-generic-federated/cobra/denuvo/my_project/denuvo_files.zip"
        project:
            retail: 'Deunvo Project Name - Retail'
            trial: 'Deunvo Project Name - Trial'
        servers: ''
        server_url: 'https://oetk2-prod.codefusion.technology'
```

- *description*: Denuvo settings. Only used if you enable denuvo wrapping on win64

## ant_local_dir

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    ant_local_dir: "Raw\\Animations\\WhitesharkAnimations"
```

- *description*: Path to Ant project files. Relative from data directory

## enable_retry

- *type*: bool
- *required*: False
- *example*:

```yaml
default:
    enable_retry: true
```

- *description*: If a function is decorated with `elipy_retry` should we retry if it fails

## tasks_to_kill

- *type*: list[string]
- *required*: False
- *example*:

```yaml
default:
    tasks_to_kill:
        - 'AvalancheCLI.exe'
        - 'FrostyIsoTool.exe'
        - 'msbuild.exe'
```

- *description*: What tasks should we try and kill when running `running_processes.kill()`

## secrets

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
    secrets:
        # Get Casablanca server connection keys for bundling into Frosty server builds
        - where:
            build_type: frosty
            platform: server
          url: 'https://ess.ea.com'
          namespace:  'target-ess-namespace'
          role_id: '0fea71e9-bc7e-acba-613e-4364869bceb6'
          secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
          files:
            - path: '/project_folder/game-server.client.int.eadp'
              key: 'key'
              to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\project-GAME-SERVER.client.int.eadp.ea.com.key'
            - path: '/project_folder/game-server.client.int.eadp'
              key: 'crt'
```

- *description*: What secrets to pull from ESS and where to store them

## shift_submission_path

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    shift_submission_path: "\\\\my_studio_share.dre.ea.com\\builds\\Shift\\auto_submissions"
```

- *description*: Where to store builds that we want Shift to upload

## shift_tool_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    shift_tool_url: "https://artifacts.ea.com/artifactory/list/dre-generic-federated/cobra/shiftsubmission/4.0.1"
```

- *description*: Where to download the shift tool from

## shift_config_file

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    shift_config_file: "shift_config_bct.yml"
```

- *description*: Name of the Shift config file. This file dictates what files get upload and where to.

## symbol_stores_share

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    symbol_stores_share: "\\\\my_studio_share.dre.ea.com\\project\\Autobuilds"
```

- *description*: Where do we store the symbol stores for Microsoft and Sony platforms

## symbols_stores_folder

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    symbols_stores_folder: "symbol_services"
```

- *description*: What should be the name of the folder that we store symbol stores in

## symbol_stores_suffix

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    symbol_stores_suffix: "SymStore
```

- *description*: SymbolStore suffix to the actual SymbolStore folder name

## game_binaries

- *type*: list[string]
- *required*: False
- *example*:

```yaml
default:
    game_binaries: ['project.exe', 'project_trial.exe']
```

- *description*: Used to help determin what binaries should we should run the DenuvoPEPatcher.exe on

## metrics_port

- *type*: int
- *required*: False
- *example*:

```yaml
default:
    metrics_port: 80
another_location:
    metrics_port: 443
```

- *description*: What port should we use when pushing data to the ELIPY metrics endpoint

## metrics_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
```

- *description*: What is the URL for the ELIPY metrics elasticsearch server

## jenkins_metrics_port

- *type*: int
- *required*: False
- *example*:

```yaml
default:
    jenkins_metrics_port: 443
```

- *description*: What port should we use when pushing data to the jenkins metrics endpoint

## jenkins_metrics_url

- *type*:
- *required*: False
- *example*:

```yaml
default:
    jenkins_metrics_url: "https://studio-metrics-eck.dre.ea.com"
```

- *description*: Where should we push custom error metrics to. This is rarely used these days but can be helpful if we wanted to monitor a specific event. Should probably use Sentry moving forward.

## required_vault_files

- *type*: dict
- *required*: False
- *example*:

```yaml
default:
    required_vault_files:
        win64:
          - 'bfv.exe'
          - 'bfvTrial.exe'
          - 'bom.fb2'
          - 'ops_chain.zip'
          - 'cas.cat'
          - 'cas_01.cas'
        ps4:
          - 'bom.fb2'
          - 'ops_chain.zip'
          - 'cas.cat'
          - 'cas_01.cas'
```

- *description*: What files are required when vaulting. This is used for a inital basic check.

## overlord_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    overlord_url: "https://overlord.apps.prod.frosting.ea.com"
```

- *description*: Target overload server for icepick

## filefusion_url

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    filefusion_url: https://filefusion-file-fusion.apps.prod.frosting.ea.com"
```

- *description*: Target filefusion server for icepick

## hailstorm_server

- *type*: string
- *required*: False
- *example*:

```yaml
default:
    hailstorm_server: "eac-fb-hstorm01.eac.ad.ea.com"
```

- *description*: target hailstorm server for icepick cook

## perforce_maxwait

- *type*: int
- *required*: False
- *example*:

```yaml
default:
    perforce_maxwait: 60
```

- *description*: Time, in seconds, before a Perforce command's connection times out. Gets added to Perforce commands as the "-vnet.maxwait" arg. For more information, see <https://help.perforce.com/helix-core/server-apps/cmdref/current/Content/CmdRef/configurables.alphabetical.html#net.maxwait>.

## perforce_retries

- *type*: int
- *required*: False
- *example*:

```yaml
default:
    perforce_retries: 3
```

- *description*: Number of times to retry Perforce commands, using the global option "-r".
