import com.ea.lib.LibVaultSecretHandler
import spock.lang.Specification
import spock.lang.Unroll

class LibVaultSecretHandlerSpec extends Specification {

    @Unroll
    void 'isValidVaultSecret should validate correct secret structure'() {
        given:
        def validSecret = [
            vault_secret_path: 'secret/path',
            target_env_var   : 'ENV_VAR',
            vault_secret_key : 'secret_key',
        ]

        when:
        def result = LibVaultSecretHandler.isValidVaultSecret(validSecret)

        then:
        result == true
    }

    void 'isValidVaultSecret fails verbosely if list passed instead of map'() {
        when:
        LibVaultSecretHandler.isValidVaultSecret(wrongType)

        then:

        def ex = thrown(IllegalArgumentException)
        ex.message == 'A vault secret must be a map and isValidVaultSecret validates individual secrets, not lists of secrets.'

        where:
        scenario                               | wrongType
        'Attempt to validate  list of secrets' | []
    }

    @Unroll
    void 'isValidVaultSecret should throw exception when secret map missing required keys'() {
        when:
        LibVaultSecretHandler.isValidVaultSecret(testMap)

        then:
        thrown(IllegalArgumentException)

        where:
        scenario                    | testMap
        'missing vault_secret_path' | [target_env_var: 'ENV_VAR', vault_secret_key: 'key']
        'missing target_env_var'    | [vault_secret_path: 'path', vault_secret_key: 'key']
        'missing vault_secret_key'  | [vault_secret_path: 'path', target_env_var: 'ENV_VAR']
        'all fields are missing'    | [:]
    }

    @Unroll
    void 'constructor should correctly initialize with #scenario'() {
        when:
        def handler = new LibVaultSecretHandler()
        handler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(inputSecrets) // TODO: refactor later

        then:
        handler.vault_secrets == expectedResult

        where:
        scenario                     | inputSecrets                                                                     | expectedResult
        'single path'                | [[vault_secret_path: 'path1',
                                         target_env_var   : 'ENV1',
                                         vault_secret_key : 'key1']]                                                    | [[path        : 'path1',
                                                                                                                            secretValues: [[envVar: 'ENV1', vaultKey: 'key1', isRequired: true]]]]
        'multiple secrets same path' | [[vault_secret_path: 'path1', target_env_var: 'ENV1', vault_secret_key: 'key1'],
                                        [vault_secret_path: 'path1', target_env_var: 'ENV2', vault_secret_key: 'key2']] | [[path        : 'path1',
                                                                                                                            secretValues: [[envVar: 'ENV1', vaultKey: 'key1', isRequired: true],
                                                                                                                                            [envVar: 'ENV2', vaultKey: 'key2', isRequired: true]]]]
        'empty list'                 | []                                                                               | []
    }

    void 'constructor should throw exception for invalid secrets'() {
        when:
        new LibVaultSecretHandler()
        handler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(invalidSecrets) // TODO: refactor later

        then:
        thrown(IllegalArgumentException)

        where:
        invalidSecrets << [
            [[not_a_valid_secret: true]],
            [[:]],
        ]
    }

    void 'mergeVaultSecrets should throw exception for invalid secret'() {
        given:
        def handler = new LibVaultSecretHandler()
        def invalidSecrets = [[not_a_valid_secret: true]]

        when:
        handler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(invalidSecrets) // TODO: refactor later

        then:
        thrown(IllegalArgumentException)
    }

    @Unroll
    void 'constructor correctly initializes: #scenario'() {
        when:
        def handler = new LibVaultSecretHandler()
        handler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(inputSecrets) // TODO: refactor later

        then:
        handler.vault_secrets == expectedResult

        where:
        scenario                     | inputSecrets                                                                     | expectedResult
        'single path'                | [[vault_secret_path: 'path1',
                                         target_env_var   : 'ENV1',
                                         vault_secret_key : 'key1']]                                                    | [[path        : 'path1',
                                                                                                                            secretValues: [[envVar: 'ENV1', vaultKey: 'key1', isRequired: true]]]]
        'multiple secrets same path' | [[vault_secret_path: 'path1', target_env_var: 'ENV1', vault_secret_key: 'key1'],
                                        [vault_secret_path: 'path1', target_env_var: 'ENV2', vault_secret_key: 'key2']] | [[path        : 'path1',
                                                                                                                            secretValues: [[envVar: 'ENV1', vaultKey: 'key1', isRequired: true],
                                                                                                                                                [envVar: 'ENV2', vaultKey: 'key2', isRequired: true]]]]
        'empty list'                 | []                                                                               | []
    }

    @Unroll
    void 'targetEnvVarsAreUnique should #scenario'() {
        when:
        def result = null
        def exceptionThrown = false
        try {
            result = LibVaultSecretHandler.throwIfTargetEnvVarsAreNotUnique(secrets)
        } catch (IllegalArgumentException e) {
            exceptionThrown = true
            result = e.message
        }

        then:
        exceptionThrown == !expectedToPass
        if (!expectedToPass) {
            result == expectedMessage
        }

        where:
        scenario                        | secrets                                                                          | expectedToPass | expectedMessage
        'pass with unique variables'    | [[vault_secret_path: 'path1', target_env_var: 'VAR1', vault_secret_key: 'key1'],
                                           [vault_secret_path: 'path2', target_env_var: 'VAR2', vault_secret_key: 'key2']] | true           | null
        'fail with single duplicate'    | [[vault_secret_path: 'path1', target_env_var: 'VAR1', vault_secret_key: 'key1'],
                                           [vault_secret_path: 'path2', target_env_var: 'VAR2', vault_secret_key: 'key2'],
                                           [vault_secret_path: 'path3', target_env_var: 'VAR1', vault_secret_key: 'key3']] | false          | 'Vault secrets must have unique target_env_var names. Duplicated variables: VAR1'
        'fail with multiple duplicates' | [[vault_secret_path: 'path1', target_env_var: 'VAR1', vault_secret_key: 'key1'],
                                           [vault_secret_path: 'path2', target_env_var: 'VAR2', vault_secret_key: 'key2'],
                                           [vault_secret_path: 'path3', target_env_var: 'VAR1', vault_secret_key: 'key3'],
                                           [vault_secret_path: 'path4', target_env_var: 'VAR2', vault_secret_key: 'key4']] | false          | 'Vault secrets must have unique target_env_var names. Duplicated variables: VAR1, VAR2'
        'pass with empty list'          | []                                                                               | true           | null
        'pass with single entry'        | [[vault_secret_path: 'path1', target_env_var: 'VAR1', vault_secret_key: 'key1']] | true           | null
    }

}
