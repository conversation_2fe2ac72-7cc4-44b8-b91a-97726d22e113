"""
artifactory_client.py

Client for downloading artifacts from Artifactory.
"""

import os
import pathlib

from elipy2 import LOGGER, core
from elipy2.exceptions import (
    CoreException,
)


# pylint: disable=too-few-public-methods
class ArtifactoryClient:
    """
    Client used for downloading artifacts from Artifactory.
    Contains functions for downloading and unzipping artifacts.
    @param username: Artifactory username
    @param api_key: Artifactory API key
    """

    def __init__(self, username: str, api_key: str):
        self.username = username
        self.api_key = api_key
        self.base_urls = [
            # AF2 URL
            "https://artifacts.ea.com/artifactory/",
            # Fallback AF1 URL TODO: remove when transition is complete
            "https://artifactory.eu.ea.com/artifactory/",
        ]
        LOGGER.info(
            "Initializing Artifactory client with base URL and fallbacks {}".format(
                ", ".join(self.base_urls)
            )
        )

    def download_artifact(
        self,
        source_path: str,
        target_directory: str,
        unzip_if_archive: bool = True,
        force_download: bool = False,
    ) -> str:
        """
        Downloads the given artifact from Artifactory and unzips it into the target directory.
        Creates the target directory if it doesn't exist. If the directory already exists
        it doesn't download anything, to force download, set force_download to True.
        :param source_path: Source path for the artifact to download. Copy from Artifactory.
        :param target_directory: The directory to download and unzip (if applicable) into.
        :param unzip_if_archive: Whether to unzip if the file is an archive.
        :param force_download: Set to True to force a download even when the target
        directory already exists
        :return: The path to the downloaded artifact. If the artifact is an archive, and it
        was unzipped, the path to the folder it was unzipped into.
        """
        source_extension = os.path.splitext(source_path)
        file_extension = source_extension[1]
        head_tail = os.path.split(source_path)
        filename = head_tail[1]
        full_path = os.path.join(target_directory, filename)
        if not os.path.exists(full_path) or force_download:
            if os.path.exists(full_path) and force_download:
                LOGGER.info(
                    "   Path %s already exists, but force_download is set to True."
                    " Redownloading.",
                    target_directory,
                )
            LOGGER.info("   Downloading %s into %s...", source_path, target_directory)
            pathlib.Path(target_directory).mkdir(exist_ok=True, parents=True)

            # Download from the primary URL and fall back to secondaries URLs if needed
            urls_and_fallbacks = [
                "{}{}".format(base_url, source_path) for base_url in self.base_urls
            ]

            # Curl authentication
            curl_authentication = "{}:{}".format(self.username, self.api_key)

            # Test each URL until one succeeds
            last_curl_exception = None
            for idx, url in enumerate(urls_and_fallbacks):
                try:
                    LOGGER.info("   Trying to download from %s", url)
                    core.run(
                        [
                            "curl",
                            "-f",
                            "-u",
                            curl_authentication,
                            url,
                            "-o",
                            full_path,
                        ]
                    )
                    break  # Exit loop if download is successful
                except CoreException as curl_exception:
                    LOGGER.error("   Failed to download from %s: %s", url, curl_exception)

                    # If all URLs fail, raise the last exception
                    if idx == len(urls_and_fallbacks) - 1:
                        raise curl_exception
                    else:
                        last_curl_exception = curl_exception

            if self._should_unzip(file_extension, unzip_if_archive):
                LOGGER.info("   Unzipping %s...", full_path)
                core.extract_zip(full_path, target_directory)
        else:
            LOGGER.info(
                "   Path %s already exists. Not downloading artifact %s. To force download"
                ", set force_download to True or delete the directory",
                target_directory,
                source_path,
            )
        LOGGER.info("   Done.")
        if self._should_unzip(file_extension, unzip_if_archive):
            return os.path.join(target_directory, os.path.splitext(filename)[0])
        return full_path

    @staticmethod
    def _should_unzip(file_extension: str, unzip_if_archive: bool):
        """
        Determines whether to unzip the archive
        :param file_extension: The file extension of the artifact
        :param unzip_if_archive: whether to unzipp if it's an archive
        :return: True if it's an archive, and it should be unzipped, False otherwise
        """
        return file_extension in [".zip", ".rar", ".gz"] and unzip_if_archive
