   UploadToSpin.run()
      GetMasterFile.get_masterfile(http://example.com/dummy)
      GetBranchFile.get_branchfile(test, null)
      UploadToSpin.pipeline(groovy.lang.Closure)
         UploadToSpin.allowBrokenBuildClaiming()
         UploadToSpin.timestamps()
         UploadToSpin.echo(Executing on agent [label:test-label])
         UploadToSpin.stage(Set Display Name, groovy.lang.Closure)
            UploadToSpin.SetDisplayName({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, [null, 123])
         UploadToSpin.stage(Upload to Spin, groovy.lang.Closure)
            UploadToSpin.P4SyncDefault(class com.ea.project.all.All, {standard_jobs_settings={slack_channel_spin=#test-channel}}, folder, code_branch, code, 123)
            UploadToSpin.InjectVaultSecrets({standard_jobs_settings={slack_channel_spin=#test-channel}}, class com.ea.project.all.All, groovy.lang.Closure)
               LibCommonNonCps.get_setting_value({slack_channel_spin=#test-channel}, [], vault_secrets_project, [], class com.ea.project.all.All)
               LibCommonNonCps.get_setting_value({slack_channel_spin=#test-channel}, [], vault_secrets_branch, [], class com.ea.project.all.All)
               LibVaultSecretHandler.mergeVaultSecrets([{vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_USER, vault_secret_key=username}, {vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_TOKEN, vault_secret_key=reference_token}, {vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_USER, vault_secret_key=username}, {vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_TOKEN, vault_secret_key=reference_token}])
                  LibVaultSecretHandler.isValidVaultSecret({vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_USER, vault_secret_key=username})
                  LibVaultSecretHandler.isValidVaultSecret({vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_TOKEN, vault_secret_key=reference_token})
                  LibVaultSecretHandler.isValidVaultSecret({vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_USER, vault_secret_key=username})
                  LibVaultSecretHandler.isValidVaultSecret({vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_TOKEN, vault_secret_key=reference_token})
                  LibVaultSecretHandler.throwIfTargetEnvVarsAreNotUnique([{vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_USER, vault_secret_key=username}, {vault_secret_path=artifacts/automation/dre-pypi-federated/ro, target_env_var=AF2_TOKEN, vault_secret_key=reference_token}, {vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_USER, vault_secret_key=username}, {vault_secret_path=artifacts/automation/dre-generic-federated/ro, target_env_var=AF2_GENERIC_TOKEN, vault_secret_key=reference_token}])
               LibVaultSecretHandler.getVaultConfiguration()
               InjectVaultSecrets.withVault({configuration={engineVersion=2, failIfNotFound=true, timeout=60, vaultCredentialId=vault-auth-dummy, vaultUrl=http://127.0.0.1:8200}, vaultSecrets=[{path=artifacts/automation/dre-pypi-federated/ro, secretValues=[{envVar=AF2_USER, vaultKey=username, isRequired=true}, {envVar=AF2_TOKEN, vaultKey=reference_token, isRequired=true}]}, {path=artifacts/automation/dre-generic-federated/ro, secretValues=[{envVar=AF2_GENERIC_USER, vaultKey=username, isRequired=true}, {envVar=AF2_GENERIC_TOKEN, vaultKey=reference_token, isRequired=true}]}]}, groovy.lang.Closure)
                  UploadToSpin.bat(install)
            UploadToSpin.string({credentialsId=test, variable=TEST})
            UploadToSpin.withCredentials([{credentialsId=test, variable=TEST}], groovy.lang.Closure)
               UploadToSpin.bat(elipy submit_to_spin --code-branch code_branch --code-changelist 123 --data-branch data_branch --data-changelist 123 --platform linuxserver --format digital --config final --region ww)
         UploadToSpin.script(groovy.lang.Closure)
            UploadToSpin.SlackMessageNew({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=null.null.123, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, #test-channel, test)
