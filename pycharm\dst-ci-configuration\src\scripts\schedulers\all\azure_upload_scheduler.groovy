package scripts.schedulers.all

import com.ea.project.GetMasterFile
import com.ea.project.GetBranchFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * azure_upload_scheduler.groovy
 */
pipeline {
    agent {
        node {
            label(env.node_label)
            customWorkspace(project.workspace_root)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.code_changelist])
            }
        }
        stage('Upload to Azure') {
            steps {
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                script {
                    String additionalTools = params.additional_tools_to_include
                    def optional_args = ''
                    optional_args += params.source ? ' --source ' + params.source : ''
                    optional_args += params.destination ? ' --destination ' + params.destination : ''
                    optional_args += additionalTools ? additionalTools.split(',').collect { " --additional-tools-to-include ${it.trim()}" }.join('') : ''
                    withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                        bat([env.elipy_call, 'copy_from_filer_to_azure',
                             '--content-type', env.content_type,
                             '--platform', env.platform,
                             '--config', env.config,
                             '--code-branch', env.code_branch,
                             '--code-changelist', params.code_changelist,
                             '--target-build-share', params.target_build_share,
                             '--secret-context', params.secret_context,
                             '--licensee', env.frostbite_licensee,
                             optional_args,
                        ].join(' '))
                    }
                }
            }
        }
    }
}

